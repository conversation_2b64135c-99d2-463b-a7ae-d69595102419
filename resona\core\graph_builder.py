"""
图谱构建器 - 将文本内容转换为结构化的用户图谱
实现业务架构中的子任务D：图结构构建
"""
import asyncio
import logging
import hashlib
import uuid
from typing import List, Dict, Optional, Any, Tuple, Set
from datetime import datetime
import re
import tiktoken

from ..models.user_models import ParsedQuery
from ..models.graph_models import (
    UserGraph, NodeType, RelationType, GraphNode, 
    GraphEdge, GraphElements
)
from ..config import settings
# from ..services.ai_service import AIService # Removed to break circular import

logger = logging.getLogger(__name__)

_ai_service_instance = None

def get_ai_service():
    """获取AIService的单例"""
    global _ai_service_instance
    if _ai_service_instance is None:
        from ..services.ai_service import AIService
        _ai_service_instance = AIService()
    return _ai_service_instance

class GraphBuilder:
    """
    图谱构建器 - 将文本内容转换为结构化图谱
    
    核心功能：
    1. 分析文本内容，提取经验、信念、情绪、话题节点
    2. 识别节点间的因果、影响、冲突、支持关系
    3. 构建完整的用户图谱结构
    4. 合并和优化图谱质量
    """
    
    def __init__(self, ai_service: Optional['AIService'] = None):
        """初始化图谱构建器"""
        self.ai_service = ai_service or get_ai_service()
        
        # Token 编码器初始化
        try:
            # 尝试为具体模型初始化编码器
            model_name = settings.llm_model.split('/')[-1].lower()
            if 'qwen' in model_name:
                # Qwen系列模型使用cl100k_base编码
                self.tokenizer = tiktoken.get_encoding("cl100k_base")
            else:
                # 默认使用GPT-3.5的编码器
                self.tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")
            logger.info(f"Token编码器初始化成功，模型: {settings.llm_model}")
        except Exception as e:
            logger.warning(f"Token编码器初始化失败: {e}，使用默认编码器")
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # 计算有效Token预算
        self._calculate_token_budget()
        
        # 配置参数
        self.similarity_threshold = 0.8  # 节点相似度阈值
        self.min_node_weight = 0.3      # 最小节点权重
        self.max_nodes_per_text = 10    # 每段文本最大节点数
        self.max_edges_per_node = 5     # 每个节点最大连边数
        
        # 缓存机制
        self._content_cache: Dict[str, GraphElements] = {}
        self._cache_max_size = 500
        
        logger.info("图谱构建器初始化完成")
    
    def _calculate_token_budget(self) -> None:
        """智能计算有效Token预算"""
        # 使用新的智能计算方法
        budget_info = settings.calculate_token_budget()
        self.token_budget = budget_info["effective_budget"]
        
        logger.info(f"Token预算智能计算完成:")
        logger.info(f"  - 模型: {settings.llm_model}")
        logger.info(f"  - 描述: {budget_info['model_description']}")
        logger.info(f"  - 上下文窗口: {budget_info['context_window']:,}")
        logger.info(f"  - 预留输出: {budget_info['max_output_tokens']:,} ({settings.llm_output_ratio:.1%})")
        logger.info(f"  - 系统提示: {budget_info['system_prompt_tokens']:,} ({settings.llm_system_ratio:.1%})")
        logger.info(f"  - 安全系数: {budget_info['safety_factor']:.1%}")
        logger.info(f"  - 有效预算: {self.token_budget:,} tokens")
        logger.info(f"  - 预算利用率: {self.token_budget/budget_info['context_window']:.1%}")
    
    def _count_tokens(self, text: str) -> int:
        """计算文本的Token数量"""
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            logger.warning(f"Token计数失败: {e}，使用字符数估算")
            # 降级策略：1 token ≈ 3-4 个字符（中文）
            return len(text) // 3
    
    def _pack_contents_by_token(self, contents: List[str]) -> List[List[str]]:
        """按Token预算将内容打包成批次"""
        if not contents:
            return []
        
        logger.info(f"开始按Token预算打包 {len(contents)} 段内容")
        
        # 预估总Token数用于自适应预算计算
        estimated_total_tokens = sum(self._count_tokens(content) for content in contents)
        
        batches = []
        current_batch = []
        current_tokens = 0
        
        # 为每段内容计算Token数
        content_tokens = [(content, self._count_tokens(content)) for content in contents]
        
        for content, token_count in content_tokens:
            # 使用自适应Token预算
            adaptive_budget = settings.calculate_adaptive_token_budget(
                batch_contents=current_batch + [content],
                estimated_input_tokens=current_tokens + token_count
            )
            current_budget = adaptive_budget["effective_budget"]
            
            # 检查单段内容是否超过预算
            if token_count > current_budget:
                logger.warning(f"单段内容超过Token预算 ({token_count} > {current_budget})，将截断")
                # 按比例截断
                ratio = current_budget / token_count * 0.9  # 留10%安全边距
                content = content[:int(len(content) * ratio)]
                token_count = self._count_tokens(content)
            
            # 检查是否可以加入当前批次
            if (current_tokens + token_count <= current_budget and 
                len(current_batch) < settings.llm_batch_max_chunks):
                current_batch.append(content)
                current_tokens += token_count
            else:
                # 当前批次已满，开始新批次
                if current_batch:
                    batches.append(current_batch)
                current_batch = [content]
                current_tokens = token_count
        
        # 添加最后一个批次
        if current_batch:
            batches.append(current_batch)
        
        logger.info(f"内容打包完成: {len(contents)} 段 → {len(batches)} 批次")
        logger.info(f"预估总Token数: {estimated_total_tokens:,}")
        for i, batch in enumerate(batches):
            batch_tokens = sum(self._count_tokens(content) for content in batch)
            # 计算该批次的自适应预算信息
            batch_budget = settings.calculate_adaptive_token_budget(
                batch_contents=batch,
                estimated_input_tokens=batch_tokens
            )
            logger.info(f"  批次 {i+1}: {len(batch)} 段内容, ~{batch_tokens:,} tokens")
            logger.info(f"    预算: {batch_budget['effective_budget']:,}, 安全系数: {batch_budget['safety_factor']:.2f}, 节点上限: {batch_budget['adaptive_max_nodes']}")
        
        return batches
    
    async def analyze_content(self, content: str, context: Optional[str] = None) -> GraphElements:
        """
        分析单个文本内容，提取图谱元素
        
        Args:
            content: 要分析的文本内容
            context: 可选的上下文信息
            
        Returns:
            GraphElements: 包含节点和边的图谱元素
        """
        logger.info(f"开始分析文本内容：{content[:50]}...")
        
        # 检查缓存
        cache_key = self._get_content_cache_key(content)
        if cache_key in self._content_cache:
            logger.info("使用缓存的分析结果")
            return self._content_cache[cache_key]
        
        try:
            # 尝试使用AI服务进行智能分析
            if self.ai_service:
                try:
                    # 检测AI服务可用性
                    connection_ok = await self.ai_service.test_connection()
                    if connection_ok:
                        logger.info("使用AI服务进行智能图谱分析")
                        graph_elements = await self._ai_analyze_content(content, context)
                    else:
                        logger.warning("AI服务连接不可用，使用降级策略")
                        graph_elements = self._fallback_analyze_content(content)
                except Exception as e:
                    logger.warning(f"AI服务测试失败: {e}，使用降级策略")
                    graph_elements = self._fallback_analyze_content(content)
            else:
                logger.warning("AI服务未初始化，使用降级策略")
                graph_elements = self._fallback_analyze_content(content)
            
            # 存入缓存
            self._cache_content_result(cache_key, graph_elements)
            
            logger.info(f"分析完成，提取到 {len(graph_elements.get_all_nodes())} 个节点，{len(graph_elements.get_all_edges())} 条边")
            return graph_elements
            
        except Exception as e:
            logger.error(f"分析文本内容失败：{e}")
            # 最终降级策略
            return self._fallback_analyze_content(content)
    
    async def _ai_analyze_content(self, content: str, context: Optional[str] = None) -> GraphElements:
        """
        使用 LangChain 进行智能图谱分析（增加超时和重试）
        
        Args:
            content: 要分析的文本内容
            context: 可选的上下文信息
            
        Returns:
            GraphElements: 包含节点和边的图谱元素
        """
        max_retries = 2  # 最多重试2次
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"🔄 第 {attempt + 1} 次尝试 LangChain 图谱分析...")
                else:
                    logger.info("🧠 使用 LangChain 图谱分析链进行智能分析...")
                
                # 检查是否有新的 LangChain 方法
                if not hasattr(self.ai_service, 'get_graph_analysis_chain'):
                    logger.warning("AI服务未提供图谱分析链，使用兼容模式")
                    return await self._ai_analyze_content_legacy(content, context)
                
                # 获取图谱分析链
                graph_chain = self.ai_service.get_graph_analysis_chain()
                
                # 使用 LangChain 链进行分析，添加超时控制
                from ..models.ai_response_models import GraphAnalysisResult
                
                # 添加超时控制
                try:
                    analysis_result: GraphAnalysisResult = await asyncio.wait_for(
                        graph_chain.ainvoke({
                            "content": content,
                            "context": context
                        }),
                        timeout=30.0  # 30秒超时
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ LangChain 分析超时 (第 {attempt + 1} 次)")
                    if attempt < max_retries:
                        await asyncio.sleep(2)  # 等待2秒后重试
                        continue
                    else:
                        raise Exception("LangChain分析超时，已达最大重试次数")
                
                logger.info(f"✅ LangChain 图谱分析成功，提取到 {len(analysis_result.nodes)} 个节点，{len(analysis_result.edges)} 条边")
                
                # 转换为 GraphElements 格式
                graph_elements = self._convert_to_graph_elements(analysis_result)
                
                return graph_elements
                
            except Exception as e:
                logger.error(f"❌ LangChain 图谱分析失败 (第 {attempt + 1} 次): {e}")
                
                if attempt < max_retries:
                    # 短暂等待后重试
                    await asyncio.sleep(1)
                    continue
                else:
                    # 最终回退到降级策略
                    logger.warning("🔄 达到最大重试次数，使用降级策略")
                    return self._fallback_analyze_content(content)
    
    async def _ai_analyze_content_legacy(self, content: str, context: Optional[str] = None) -> GraphElements:
        """传统的 AI 图谱分析方法（作为降级策略）"""
        try:
            # 保留原有实现
            prompt = self._build_graph_analysis_prompt(content, context)
            
            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=1500,
                temperature=0.3
            )
            
            graph_elements = self._parse_ai_response(response, content)
            
            logger.info("AI图谱分析完成（传统方法）")
            return graph_elements
            
        except Exception as e:
            logger.error(f"传统AI图谱分析失败：{e}")
            return self._fallback_analyze_content(content)
    
    def _convert_to_graph_elements(self, analysis_result) -> GraphElements:
        """将 LangChain 分析结果转换为 GraphElements"""
        from ..models.ai_response_models import GraphAnalysisResult
        
        # 按类型分类节点
        experience_nodes = []
        belief_nodes = []
        emotion_nodes = []
        topic_nodes = []
        
        # 节点类型映射
        node_type_map = {
            "experience": NodeType.EXPERIENCE,
            "belief": NodeType.BELIEF,
            "emotion": NodeType.EMOTION,
            "topic": NodeType.TOPIC
        }
        
        # 转换节点
        for node_model in analysis_result.nodes:
            node_type = node_type_map.get(node_model.type, NodeType.TOPIC)
            
            node = GraphNode(
                node_id=node_model.id,
                node_type=node_type,
                content=node_model.content,
                weight=node_model.weight,
                metadata=node_model.metadata
            )
            
            # 按类型分类
            if node_type == NodeType.EXPERIENCE:
                experience_nodes.append(node)
            elif node_type == NodeType.BELIEF:
                belief_nodes.append(node)
            elif node_type == NodeType.EMOTION:
                emotion_nodes.append(node)
            elif node_type == NodeType.TOPIC:
                topic_nodes.append(node)
        
        # 转换边
        relation_type_map = {
            "causes": RelationType.CAUSES,
            "influences": RelationType.INFLUENCES,
            "conflicts": RelationType.CONTRADICTS,
            "supports": RelationType.SUPPORTS,
            "triggers": RelationType.LEADS_TO,
            "correlates": RelationType.SIMILAR_TO
        }
        
        edges = []
        for edge_model in analysis_result.edges:
            relation_type = relation_type_map.get(edge_model.relation, RelationType.INFLUENCES)
            
            edge = GraphEdge(
                source_id=edge_model.source,
                target_id=edge_model.target,
                relation_type=relation_type,
                weight=edge_model.weight,
                evidence=edge_model.evidence
            )
            edges.append(edge)
        
        return GraphElements(
            experience_nodes=experience_nodes,
            belief_nodes=belief_nodes,
            emotion_nodes=emotion_nodes,
            topic_nodes=topic_nodes,
            causal_edges=edges
        )
    
    def _build_graph_analysis_prompt(self, content: str, context: Optional[str] = None) -> str:
        """构建图谱分析的提示词（时间线感知版本）"""
        
        # 检测内容是否包含时间信息
        has_timeline = '【' in content and '年' in content and '月' in content
        
        if has_timeline:
            # 时间线感知的prompt
            prompt = f"""
请仔细分析以下按时间顺序排列的文本内容，构建用户的心理认知图谱。
**🕒 重要：请充分利用时间信息来判断因果关系和发展轨迹。**

分析文本（包含时间标记）：
{content}

{f"上下文信息：{context}" if context else ""}

请按照以下JSON格式输出分析结果：

{{
    "nodes": [
        {{
            "id": "node_1",
            "type": "experience|belief|emotion|topic",
            "content": "节点内容描述",
            "weight": 0.8,
            "metadata": {{
                "confidence": 0.85,
                "keywords": ["关键词1", "关键词2"],
                "temporal_evidence": "基于时间线的证据",
                "time_period": "2022年3月" // 如果能推断出时间
            }}
        }}
    ],
    "edges": [
        {{
            "source": "node_1",
            "target": "node_2", 
            "relation": "causes|influences|conflicts|supports|triggers|correlates",
            "weight": 0.9,  // 时间证据支撑的关系可以给更高权重
            "evidence": "时间线证据：2022年的失败经历导致2023年的成长观念"
        }}
    ]
}}

**🎯 时间线分析要求：**
1. **时间因果判断**：根据时间先后顺序识别因果关系（早期经历 → 后期观念/情绪）
2. **发展轨迹识别**：识别用户的情绪变化、观念演进轨迹
3. **权重时间调整**：为有明确时间证据的关系给予更高权重（0.8-0.9）
4. **证据时间说明**：在evidence中明确说明时间线依据
5. **时间表达识别**：注意"后来"、"现在"、"当时"、"之前"等时间连接词

**⚡ 时间线关系模式：**
- 早期挫折经历 → 后期成长观念 (LEADS_TO, weight: 0.9)
- 过去迷茫情绪 → 现在清晰认知 (LEADS_TO, weight: 0.9)
- 以前的选择 → 现在的结果 (CAUSES, weight: 0.8)
- 持续的实践 → 逐渐的理解 (INFLUENCES, weight: 0.8)

【节点类型定义】：

**experience (经历体验)** - 用户的具体经历、事件、身份、行为：
- 时间相关经历："【2022年】开始创业"、"【去年】参加面试"
- 发展过程："坚持了7年"、"经过反复尝试"

**belief (信念价值观)** - 用户的价值观、原则、观点：
- 经历后的感悟："后来我明白了..."、"现在我认为..."
- 发展的观念："逐渐意识到..."、"开始相信..."

**emotion (情绪感受)** - 用户的情绪状态、心理感受：
- 时间情绪变化："当时很兴奋"、"现在很迷茫"、"后来感到失望"

**topic (话题主题)** - 讨论的具体主题、领域：
- 持续关注的话题："一直在思考的问题"

【质量要求】：
1. 优先构建基于时间证据的因果关系
2. 节点权重反映时间重要性（近期0.8-0.9，远期0.6-0.7）
3. 关系权重反映时间证据强度
4. 最多输出{self.max_nodes_per_text}个节点
5. 确保有至少2-3条时间线关系

请只返回JSON，不要包含其他内容。
"""
        else:
            # 普通的prompt（无时间信息）
            prompt = f"""
请仔细分析以下文本内容，提取用户的心理认知图谱元素。特别注意正确识别节点类型。

分析文本：
{content}

{f"上下文信息：{context}" if context else ""}

请按照以下JSON格式输出分析结果：

{{
    "nodes": [
        {{
            "id": "node_1",
            "type": "experience|belief|emotion|topic",
            "content": "节点内容描述",
            "weight": 0.8,
            "metadata": {{
                "confidence": 0.85,
                "keywords": ["关键词1", "关键词2"],
                "intensity": "high|medium|low"
            }}
        }}
    ],
    "edges": [
        {{
            "source": "node_1",
            "target": "node_2", 
            "relation": "causes|influences|conflicts|supports|triggers|correlates",
            "weight": 0.7,
            "evidence": "支撑这种关系的文本证据"
        }}
    ]
}}

【重要】节点类型详细定义和示例：

**experience (经历体验)** - 用户的具体经历、事件、身份、行为、观察：
- 身份状态："我是计算机专业的学生"、"我在一家公司工作"
- 具体经历："参加了面试"、"和朋友吵架了"、"看到同学们准备考研"
- 行为表现："我选择了这个专业"、"我经常加班"
- 成果结果："成绩还不错"、"项目完成了"

**belief (信念价值观)** - 用户的价值观、原则、观点、判断标准：
- 价值判断："实际工作能力比学历更重要"、"诚信是最重要的品质"
- 人生原则："做人要踏实"、"要为自己的选择负责"
- 观念看法："我认为..."、"我觉得..."、"我相信..."

**emotion (情绪感受)** - 用户的情绪状态、心理感受：
- 情绪状态："很焦虑"、"很纠结"、"感到压力"、"很开心"
- 心理感受："迷茫"、"困惑"、"担心"、"期待"

**topic (话题主题)** - 讨论的具体主题、领域、问题：
- 讨论主题："考研问题"、"职业选择"、"感情关系"
- 领域范围："教育"、"工作"、"生活"

【分类要求】：
1. 优先识别experience节点 - 任何具体的经历、事件、身份、行为都应该分类为experience
2. 每个文本至少应该包含1-2个experience节点
3. 明确区分experience（具体经历）和belief（观点价值观）
4. 情绪词汇分类为emotion，抽象观点分类为belief

【质量要求】：
1. 节点内容具体明确，权重在0-1之间
2. 关系有明确的文本证据支撑
3. 输出有效的JSON格式
4. 最多输出{self.max_nodes_per_text}个节点
5. 确保experience节点数量合理（通常1-3个）
"""
        
        return prompt
    
    def _parse_ai_response(self, response: str, original_content: str) -> GraphElements:
        """
        解析AI响应，构建图谱元素
        
        Args:
            response: AI的JSON响应
            original_content: 原始文本内容
            
        Returns:
            GraphElements: 解析后的图谱元素
        """
        try:
            # 使用稳定的JSON解析器
            from ..utils.json_parser import parse_ai_json
            data = parse_ai_json(response)

            if not data:
                logger.warning("JSON解析失败，使用降级策略")
                return self._fallback_analyze_content(original_content)
            
            # 提取节点
            nodes_data = data.get("nodes", [])
            edges_data = data.get("edges", [])
            
            # 构建节点
            experience_nodes = []
            belief_nodes = []
            emotion_nodes = []
            topic_nodes = []
            
            for node_data in nodes_data:
                node = self._create_node_from_data(node_data)
                
                # 按类型分类
                if node.node_type == NodeType.EXPERIENCE:
                    experience_nodes.append(node)
                elif node.node_type == NodeType.BELIEF:
                    belief_nodes.append(node)
                elif node.node_type == NodeType.EMOTION:
                    emotion_nodes.append(node)
                elif node.node_type == NodeType.TOPIC:
                    topic_nodes.append(node)
            
            # 构建边
            edges = []
            for edge_data in edges_data:
                edge = self._create_edge_from_data(edge_data)
                if edge:
                    edges.append(edge)
            
            logger.info(f"AI解析完成：{len(nodes_data)} 个节点，{len(edges)} 条边")
            
            return GraphElements(
                experience_nodes=experience_nodes,
                belief_nodes=belief_nodes,
                emotion_nodes=emotion_nodes,
                topic_nodes=topic_nodes,
                causal_edges=edges
            )
            
        except Exception as e:
            logger.error(f"解析AI响应失败：{e}")
            # 回退到降级策略
            return self._fallback_analyze_content(original_content)
    
    def _create_node_from_data(self, node_data: Dict[str, Any]) -> GraphNode:
        """从数据创建图节点"""
        node_type_map = {
            "experience": NodeType.EXPERIENCE,
            "belief": NodeType.BELIEF,
            "emotion": NodeType.EMOTION,
            "topic": NodeType.TOPIC
        }
        
        node_type = node_type_map.get(node_data.get("type", "topic"), NodeType.TOPIC)
        
        return GraphNode(
            node_id=node_data.get("id", f"node_{uuid.uuid4().hex[:8]}"),
            node_type=node_type,
            content=node_data.get("content", ""),
            weight=float(node_data.get("weight", 0.5)),
            metadata=node_data.get("metadata", {})
        )
    
    def _create_edge_from_data(self, edge_data: Dict[str, Any]) -> Optional[GraphEdge]:
        """从数据创建图边"""
        relation_type_map = {
            "causes": RelationType.CAUSES,
            "influences": RelationType.INFLUENCES,
            "conflicts": RelationType.CONTRADICTS,
            "supports": RelationType.SUPPORTS,
            "triggers": RelationType.LEADS_TO,
            "correlates": RelationType.SIMILAR_TO
        }
        
        relation_type = relation_type_map.get(edge_data.get("relation", "influences"), RelationType.INFLUENCES)
        
        source_id = edge_data.get("source")
        target_id = edge_data.get("target")
        
        if not source_id or not target_id:
            return None
        
        return GraphEdge(
            source_id=source_id,
            target_id=target_id,
            relation_type=relation_type,
            weight=float(edge_data.get("weight", 0.5)),
            evidence=edge_data.get("evidence", "")
        )
    
    def _fallback_analyze_content(self, content: str) -> GraphElements:
        """增强的降级策略 - 基于规则的智能图谱构建"""
        logger.info("🔄 使用增强降级图谱构建策略")
        logger.debug(f"分析内容: {content[:200]}...")  # 记录前200字符用于调试

        nodes = []
        edges = []
        
        # 1. 经历节点识别（Experience - 具体的经历和事件）
        experience_patterns = {
            "创业历程": ["创业", "坚持", "7年", "做生意", "经营"],
            "工作经历": ["工作", "职业", "就业", "上班", "公司"],
            "学习反思": ["学习", "反思", "研究", "思考", "总结"],
            "社交交流": ["聊天", "和别人", "交流", "沟通", "分享"],
            "执行实践": ["执行", "实际", "操作", "实施", "行动"],
            "观察对比": ["看到", "身边", "朋友", "别人", "同龄人"],
            "决策选择": ["选择", "决定", "考虑", "权衡", "判断"],
            "成长过程": ["成长", "发展", "进步", "提升", "改变"]
        }
        
        experience_nodes = []
        for exp_type, keywords in experience_patterns.items():
            if any(keyword in content for keyword in keywords):
                node = GraphNode(
                    node_id=f"exp_{exp_type}_{len(nodes)}",
                    node_type=NodeType.EXPERIENCE,
                    content=f"{exp_type}：用户的{exp_type}经历",
                    weight=0.9,
                    metadata={
                        "source": "enhanced_fallback",
                        "confidence": 0.8,
                        "category": "experience",
                        "keywords": [k for k in keywords if k in content]
                    }
                )
                nodes.append(node)
                experience_nodes.append(node)
                logger.debug(f"识别经历节点: {exp_type}")
        
        # 2. 信念节点识别（Belief - 价值观和观点）
        belief_patterns = {
            "成功标准": ["成绩", "成功", "成果", "收获", "效果", "结果"],
            "坚持理念": ["坚持", "继续", "放弃", "改变", "转换", "坚守"],
            "能力认知": ["懂得", "知识", "能力", "水平", "技能", "本事"],
            "选择观念": ["选择", "正确", "错误", "对错", "决定", "判断"],
            "比较心态": ["朋友", "别人", "对比", "比较", "差距", "相比"],
            "价值观念": ["重要", "意义", "价值", "意思", "值得", "有用"],
            "责任观": ["责任", "义务", "应该", "必须", "需要", "负责"],
            "自我认知": ["自己", "我觉得", "我认为", "我想", "我的", "个人"]
        }
        
        belief_nodes = []
        for belief_type, keywords in belief_patterns.items():
            if any(keyword in content for keyword in keywords):
                node = GraphNode(
                    node_id=f"belief_{belief_type}_{len(nodes)}",
                    node_type=NodeType.BELIEF,
                    content=f"{belief_type}：用户对{belief_type}的看法和信念",
                    weight=0.8,
                    metadata={
                        "source": "enhanced_fallback",
                        "confidence": 0.7,
                        "category": "belief",
                        "keywords": [k for k in keywords if k in content]
                    }
                )
                nodes.append(node)
                belief_nodes.append(node)
                logger.debug(f"识别信念节点: {belief_type}")
        
        # 3. 情绪节点识别（Emotion - 情感状态）
        emotion_patterns = {
            "迷茫困惑": ["迷茫", "不知道", "困惑", "不确定", "茫然", "不明白"],
            "怀疑质疑": ["怀疑", "质疑", "疑问", "不确定", "犹豫", "存疑"],
            "挫败失落": ["没成绩", "不行", "失败", "挫败", "沮丧", "失望"],
            "羡慕对比": ["看到别人", "稳定", "收入", "成功", "羡慕", "嫉妒"],
            "焦虑紧张": ["焦虑", "紧张", "担心", "不安", "恐惧", "害怕"],
            "自信不足": ["不自信", "不够", "不足", "缺乏", "差劲", "不好"],
            "积极向上": ["希望", "期待", "乐观", "积极", "正面", "向上"],
            "压力负担": ["压力", "负担", "重担", "累", "疲惫", "辛苦"]
        }
        
        emotion_nodes = []
        for emotion_type, keywords in emotion_patterns.items():
            if any(keyword in content for keyword in keywords):
                node = GraphNode(
                    node_id=f"emotion_{emotion_type}_{len(nodes)}",
                    node_type=NodeType.EMOTION,
                    content=f"{emotion_type}：用户的{emotion_type}情绪状态",
                    weight=0.8,
                    metadata={
                        "source": "enhanced_fallback",
                        "confidence": 0.8,
                        "category": "emotion",
                        "intensity": "high" if len([k for k in keywords if k in content]) > 1 else "medium",
                        "keywords": [k for k in keywords if k in content]
                    }
                )
                nodes.append(node)
                emotion_nodes.append(node)
                logger.debug(f"识别情绪节点: {emotion_type}")
        
        # 4. 话题节点识别（Topic - 讨论主题）
        topic_patterns = {
            "创业发展": ["创业", "事业", "生意", "发展", "项目", "企业"],
            "职业选择": ["工作", "职业", "选择", "方向", "道路", "岗位"],
            "能力提升": ["学习", "能力", "技能", "知识", "成长", "提升"],
            "人际关系": ["朋友", "社交", "关系", "交流", "沟通", "人脉"],
            "人生规划": ["方向", "未来", "规划", "目标", "路径", "计划"],
            "心理健康": ["心理", "情绪", "心态", "状态", "健康", "平衡"],
            "社会比较": ["社会", "别人", "同龄", "对比", "差异", "标准"],
            "自我认知": ["自我", "认知", "了解", "认识", "理解", "反思"]
        }
        
        topic_nodes = []
        for topic_type, keywords in topic_patterns.items():
            if any(keyword in content for keyword in keywords):
                node = GraphNode(
                    node_id=f"topic_{topic_type}_{len(nodes)}",
                    node_type=NodeType.TOPIC,
                    content=f"{topic_type}：关于{topic_type}的讨论",
                    weight=0.6,
                    metadata={
                        "source": "enhanced_fallback",
                        "confidence": 0.7,
                        "category": "topic",
                        "keywords": [k for k in keywords if k in content]
                    }
                )
                nodes.append(node)
                topic_nodes.append(node)
                logger.debug(f"识别话题节点: {topic_type}")
        
        # 5. 构建智能关系网络
        logger.debug("🔗 构建节点关系...")
        
        # 经历 → 情绪的关系
        for exp_node in experience_nodes:
            for emo_node in emotion_nodes:
                if self._should_connect_experience_emotion_enhanced(exp_node, emo_node):
                    edge = GraphEdge(
                        source_id=exp_node.node_id,
                        target_id=emo_node.node_id,
                        relation_type=RelationType.LEADS_TO,
                        weight=0.8,
                        evidence=f"{exp_node.content}导致了{emo_node.content}"
                    )
                    edges.append(edge)
                    logger.debug(f"连接: 经历→情绪 {exp_node.content[:15]}... → {emo_node.content[:15]}...")
        
        # 经历 → 信念的关系
        for exp_node in experience_nodes:
            for belief_node in belief_nodes:
                if self._should_connect_experience_belief_enhanced(exp_node, belief_node):
                    edge = GraphEdge(
                        source_id=exp_node.node_id,
                        target_id=belief_node.node_id,
                        relation_type=RelationType.INFLUENCES,
                        weight=0.7,
                        evidence=f"{exp_node.content}影响了{belief_node.content}"
                    )
                    edges.append(edge)
                    logger.debug(f"连接: 经历→信念 {exp_node.content[:15]}... → {belief_node.content[:15]}...")
        
        # 信念 ↔ 情绪的关系
        for belief_node in belief_nodes:
            for emo_node in emotion_nodes:
                if self._should_connect_belief_emotion_enhanced(belief_node, emo_node):
                    edge = GraphEdge(
                        source_id=belief_node.node_id,
                        target_id=emo_node.node_id,
                        relation_type=RelationType.SIMILAR_TO,
                        weight=0.6,
                        evidence=f"{belief_node.content}与{emo_node.content}相关"
                    )
                    edges.append(edge)
                    logger.debug(f"连接: 信念↔情绪 {belief_node.content[:15]}... ↔ {emo_node.content[:15]}...")
        
        # 话题与其他节点的关系
        for topic_node in topic_nodes:
            related_nodes = experience_nodes + belief_nodes + emotion_nodes
            for node in related_nodes[:10]:  # 限制连接数量避免过多
                if self._should_connect_topic_enhanced(topic_node, node):
                    edge = GraphEdge(
                        source_id=topic_node.node_id,
                        target_id=node.node_id,
                        relation_type=RelationType.SIMILAR_TO,
                        weight=0.5,
                        evidence=f"{topic_node.content}与{node.content}相关"
                    )
                    edges.append(edge)
        
        # 6. 如果没有识别到任何节点，创建基本的通用节点
        if not nodes:
            logger.warning("关键词匹配未识别到任何节点，创建基本通用节点")

            # 创建基本的用户表达节点
            basic_node = GraphNode(
                node_id=f"basic_expression_{uuid.uuid4().hex[:8]}",
                node_type=NodeType.TOPIC,
                content=f"用户表达：{content[:100]}..." if len(content) > 100 else content,
                weight=0.5,
                metadata={
                    "source": "basic_fallback",
                    "confidence": 0.6,
                    "category": "general_expression",
                    "content_length": len(content)
                }
            )
            nodes.append(basic_node)
            topic_nodes.append(basic_node)

            # 如果内容较长，再创建一个情绪节点
            if len(content) > 50:
                emotion_node = GraphNode(
                    node_id=f"basic_emotion_{uuid.uuid4().hex[:8]}",
                    node_type=NodeType.EMOTION,
                    content="用户情绪状态：基于内容表达的情绪倾向",
                    weight=0.4,
                    metadata={
                        "source": "basic_fallback",
                        "confidence": 0.5,
                        "category": "general_emotion"
                    }
                )
                nodes.append(emotion_node)
                emotion_nodes.append(emotion_node)

                # 连接基本节点
                edge = GraphEdge(
                    source_id=basic_node.node_id,
                    target_id=emotion_node.node_id,
                    relation_type=RelationType.LEADS_TO,
                    weight=0.5,
                    evidence="用户表达反映了其情绪状态"
                )
                edges.append(edge)

        logger.info(f"✅ 增强降级分析完成: {len(nodes)} 个节点，{len(edges)} 条边")

        return GraphElements(
            experience_nodes=experience_nodes,
            belief_nodes=belief_nodes,
            emotion_nodes=emotion_nodes,
            topic_nodes=topic_nodes,
            causal_edges=edges
        )
    
    def _should_connect_experience_emotion_enhanced(self, exp_node: GraphNode, emo_node: GraphNode) -> bool:
        """判断经历和情绪是否应该连接（增强版）"""
        exp_keywords = exp_node.metadata.get("keywords", [])
        emo_keywords = emo_node.metadata.get("keywords", [])
        
        # 创业经历 → 迷茫/挫败情绪
        if any("创业" in k for k in exp_keywords) and any(k in ["迷茫", "不行", "没成绩", "挫败"] for k in emo_keywords):
            return True
        
        # 执行经历 → 挫败情绪
        if any("执行" in k for k in exp_keywords) and any(k in ["不行", "失败", "挫败"] for k in emo_keywords):
            return True
        
        # 观察对比 → 怀疑/羡慕情绪
        if any(k in ["看到", "朋友", "别人"] for k in exp_keywords) and any(k in ["怀疑", "羡慕", "对比"] for k in emo_keywords):
            return True
        
        # 学习反思 → 困惑/不确定
        if any(k in ["学习", "反思"] for k in exp_keywords) and any(k in ["困惑", "不确定", "迷茫"] for k in emo_keywords):
            return True
        
        return False
    
    def _should_connect_experience_belief_enhanced(self, exp_node: GraphNode, belief_node: GraphNode) -> bool:
        """判断经历和信念是否应该连接（增强版）"""
        exp_keywords = exp_node.metadata.get("keywords", [])
        belief_keywords = belief_node.metadata.get("keywords", [])
        
        # 创业经历 → 成功标准/坚持理念
        if any("创业" in k for k in exp_keywords) and any(k in ["成绩", "坚持", "成功"] for k in belief_keywords):
            return True
        
        # 学习反思 → 能力认知
        if any(k in ["学习", "反思"] for k in exp_keywords) and any(k in ["懂得", "能力", "知识"] for k in belief_keywords):
            return True
        
        # 观察对比 → 比较心态
        if any(k in ["看到", "朋友", "别人"] for k in exp_keywords) and any(k in ["朋友", "别人", "对比"] for k in belief_keywords):
            return True
        
        # 决策选择 → 选择观念
        if any(k in ["选择", "决定"] for k in exp_keywords) and any(k in ["选择", "正确", "决定"] for k in belief_keywords):
            return True
        
        return False
    
    def _should_connect_belief_emotion_enhanced(self, belief_node: GraphNode, emo_node: GraphNode) -> bool:
        """判断信念和情绪是否应该连接（增强版）"""
        belief_keywords = belief_node.metadata.get("keywords", [])
        emo_keywords = emo_node.metadata.get("keywords", [])
        
        # 选择观念 → 怀疑情绪
        if any(k in ["选择", "正确", "决定"] for k in belief_keywords) and any(k in ["怀疑", "质疑", "不确定"] for k in emo_keywords):
            return True
        
        # 比较心态 → 羡慕/失落情绪
        if any(k in ["朋友", "别人", "对比"] for k in belief_keywords) and any(k in ["羡慕", "失落", "对比"] for k in emo_keywords):
            return True
        
        # 成功标准 → 挫败情绪
        if any(k in ["成绩", "成功", "结果"] for k in belief_keywords) and any(k in ["挫败", "失望", "没成绩"] for k in emo_keywords):
            return True
        
        # 能力认知 → 自信情绪
        if any(k in ["能力", "懂得", "知识"] for k in belief_keywords) and any(k in ["不自信", "不足", "不够"] for k in emo_keywords):
            return True
        
        return False
    
    def _should_connect_topic_enhanced(self, topic_node: GraphNode, other_node: GraphNode) -> bool:
        """判断话题和其他节点是否应该连接（增强版）"""
        topic_keywords = topic_node.metadata.get("keywords", [])
        other_keywords = other_node.metadata.get("keywords", [])
        
        # 增强的关键词匹配
        common_keywords = set(topic_keywords) & set(other_keywords)
        if len(common_keywords) > 0:
            return True
        
        # 语义相关性匹配
        topic_content = topic_node.content.lower()
        other_content = other_node.content.lower()
        
        # 创业话题与相关节点
        if "创业" in topic_content and any(k in other_content for k in ["创业", "事业", "生意", "坚持"]):
            return True
        
        # 职业话题与相关节点
        if "职业" in topic_content and any(k in other_content for k in ["工作", "选择", "方向", "职业"]):
            return True
        
        return False
    
    async def build_user_graph(self, contents: List[str], 
                             parsed_query: Optional[ParsedQuery] = None,
                             user_context: Optional[str] = None) -> UserGraph:
        """
        构建完整的用户图谱（批量优化版本）
        
        Args:
            contents: 用户的文本内容列表
            parsed_query: 解析后的用户查询
            user_context: 用户上下文信息
            
        Returns:
            UserGraph: 完整的用户图谱
        """
        logger.info(f"🚀 开始批量构建用户图谱，文本数量：{len(contents)}")
        
        start_time = datetime.now()
        
        # 使用批量分析减少LLM调用
        merged_elements = await self.analyze_contents_batch(contents, user_context)
        
        analysis_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"📊 批量分析完成，耗时: {analysis_time:.2f}秒")
        
        # 构建最终图谱
        user_graph = self._build_final_user_graph(merged_elements, user_context)
        
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"✅ 用户图谱批量构建完成：{len(user_graph.nodes)} 个节点，{len(user_graph.edges)} 条边，总耗时: {total_time:.2f}秒")
        return user_graph
    
    def merge_graph_elements(self, elements_list: List[GraphElements]) -> GraphElements:
        """
        合并多个图谱元素集合
        
        Args:
            elements_list: 图谱元素列表
            
        Returns:
            GraphElements: 合并后的图谱元素
        """
        logger.info(f"开始合并 {len(elements_list)} 个图谱元素集合")
        
        all_nodes = []
        all_edges = []
        
        # 收集所有节点和边
        for elements in elements_list:
            all_nodes.extend(elements.get_all_nodes())
            all_edges.extend(elements.get_all_edges())
        
        # 按类型分类节点
        experience_nodes = [n for n in all_nodes if n.node_type == NodeType.EXPERIENCE]
        belief_nodes = [n for n in all_nodes if n.node_type == NodeType.BELIEF]
        emotion_nodes = [n for n in all_nodes if n.node_type == NodeType.EMOTION]
        topic_nodes = [n for n in all_nodes if n.node_type == NodeType.TOPIC]
        
        logger.info(f"合并完成：{len(all_nodes)} 个节点，{len(all_edges)} 条边")
        return GraphElements(
            experience_nodes=experience_nodes,
            belief_nodes=belief_nodes,
            emotion_nodes=emotion_nodes,
            topic_nodes=topic_nodes,
            causal_edges=all_edges
        )
    
    def _build_final_user_graph(self, elements: GraphElements, user_context: Optional[str] = None) -> UserGraph:
        """构建最终的用户图谱"""
        # 创建图谱
        user_graph = UserGraph(user_id="temp_user", created_at=datetime.now())
        
        # 添加节点
        for node in elements.get_all_nodes():
            user_graph.add_node(node)
        
        # 添加边
        for edge in elements.get_all_edges():
            user_graph.add_edge(edge)
        
        return user_graph
    
    def validate_graph_structure(self, graph: UserGraph) -> bool:
        """
        验证图谱结构的合理性
        
        Args:
            graph: 要验证的用户图谱
            
        Returns:
            bool: 图谱结构是否合理
        """
        try:
            # 基础验证
            if not graph.nodes:
                logger.warning("图谱没有节点")
                return False
            
            logger.info("图谱结构验证通过")
            return True
            
        except Exception as e:
            logger.error(f"图谱结构验证失败：{e}")
            return False
    
    def _get_content_cache_key(self, content: str) -> str:
        """生成内容缓存键"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _cache_content_result(self, cache_key: str, result: GraphElements) -> None:
        """缓存内容分析结果"""
        if len(self._content_cache) < self._cache_max_size:
            self._content_cache[cache_key] = result
    
    async def close(self) -> None:
        """关闭图谱构建器"""
        if self.ai_service:
            await self.ai_service.close()
        logger.info("图谱构建器已关闭")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "content_cache_size": len(self._content_cache),
            "max_cache_size": self._cache_max_size
        }
    
    async def analyze_contents_batch(self, contents: List[str], context: Optional[str] = None) -> GraphElements:
        """
        批量分析多段内容，使用合并Prompt减少LLM调用
        
        Args:
            contents: 要分析的文本内容列表
            context: 可选的上下文信息
            
        Returns:
            GraphElements: 合并后的图谱元素
        """
        if not contents:
            return GraphElements([], [], [], [], [])
        
        logger.info(f"🚀 开始批量分析 {len(contents)} 段内容")
        
        try:
            # 按Token预算打包内容
            content_batches = self._pack_contents_by_token(contents)
            
            if not content_batches:
                logger.warning("内容打包后为空，使用降级策略")
                return self._fallback_analyze_multiple_contents(contents)
            
            # 并行处理所有批次
            batch_tasks = []
            for i, batch in enumerate(content_batches):
                task = self._analyze_content_batch_single(batch, context, i+1)
                batch_tasks.append(task)
            
            logger.info(f"⚡ 启动 {len(batch_tasks)} 个批量分析任务")
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 合并所有批次的结果
            valid_results = []
            for i, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"❌ 批次 {i+1} 分析失败: {result}")
                elif result is not None:
                    valid_results.append(result)
            
            if not valid_results:
                logger.warning("所有批次分析失败，使用降级策略")
                return self._fallback_analyze_multiple_contents(contents)
            
            # 合并结果
            merged_elements = self.merge_graph_elements(valid_results)
            
            logger.info(f"✅ 批量分析完成: {len(valid_results)}/{len(content_batches)} 批次成功")
            return merged_elements
            
        except Exception as e:
            logger.error(f"批量分析失败: {e}")
            return self._fallback_analyze_multiple_contents(contents)
    
    async def _analyze_content_batch_single(self, batch_contents: List[str], 
                                          context: Optional[str], 
                                          batch_index: int) -> GraphElements:
        """分析单个内容批次（支持自适应批量和超时重试）"""
        current_batch = batch_contents.copy()
        # max_retry_levels = 3  # 最多3级减小 - 暂时注释掉重试机制
        
        # 暂时禁用重试机制，直接尝试一次
        retry_level = 0
        try:
            # 计算批次Token数和自适应预算
            batch_tokens = sum(self._count_tokens(content) for content in current_batch)
            adaptive_budget = settings.calculate_adaptive_token_budget(
                batch_contents=current_batch,
                estimated_input_tokens=batch_tokens
            )
            
            retry_suffix = f" (重试级别{retry_level})" if retry_level > 0 else ""
            logger.info(f"📝 分析批次 {batch_index}: {len(current_batch)} 段内容{retry_suffix}")
            logger.info(f"  Token统计: 输入 {batch_tokens:,}, 预算 {adaptive_budget['effective_budget']:,}")
            logger.info(f"  自适应配置: 安全系数 {adaptive_budget['safety_factor']:.2f}, 节点上限 {adaptive_budget['adaptive_max_nodes']}")
            
            # 构建批量分析的Prompt
            batch_prompt = self._build_batch_analysis_prompt(
                current_batch, 
                context, 
                max_nodes_per_text=adaptive_budget['adaptive_max_nodes']
            )
            
            # 检测AI服务可用性
            if self.ai_service:
                try:
                    connection_ok = await self.ai_service.test_connection()
                    if connection_ok:
                        # 使用流式返回进行分析，提供更精确的超时控制
                        print(f"\n[AI分析] 批次 {batch_index}: {len(current_batch)} 段内容")
                        response = await self.ai_service.get_completion_stream(
                            prompt=batch_prompt,
                            max_tokens=adaptive_budget["max_output_tokens"],
                            temperature=0.3,
                            timeout=120.0  # 使用固定的120秒超时，适合复杂图谱分析
                        )
                        print(f"\n[AI分析完成] 批次 {batch_index}")
                        return self._parse_batch_ai_response(response, current_batch)
                    else:
                        logger.warning(f"AI服务不可用，批次 {batch_index} 使用降级策略")
                        return await self._fallback_analyze_batch(current_batch)
                except (asyncio.TimeoutError, Exception) as e:
                    if isinstance(e, asyncio.TimeoutError):
                        logger.warning(f"⏰ 批次 {batch_index} AI请求超时 (Level {retry_level})")
                    else:
                        logger.warning(f"AI服务调用失败: {e}，批次 {batch_index} 重试级别 {retry_level}")
                    
                    # 暂时注释掉重试逻辑，直接使用降级策略
                    logger.warning(f"🔄 批次 {batch_index} 直接使用降级策略（重试机制已禁用）")
                    return await self._fallback_analyze_batch(current_batch)
            
            # 如果AI服务不可用，直接使用降级策略
            logger.warning(f"AI服务不可用，批次 {batch_index} 使用降级策略")
            return await self._fallback_analyze_batch(current_batch)
                
        except Exception as e:
            logger.error(f"批次 {batch_index} 分析失败 (Level {retry_level}): {e}")
            # 暂时注释掉重试逻辑，直接使用降级策略
            logger.warning(f"🔄 批次 {batch_index} 直接使用降级策略（重试机制已禁用）")
            return await self._fallback_analyze_batch(current_batch)
    
    def _build_batch_analysis_prompt(self, batch_contents: List[str], context: Optional[str] = None, max_nodes_per_text: int = 10) -> str:
        """构建批量分析的Prompt（时间线感知版本）"""
        
        # 检测是否有时间线内容
        has_timeline = any('【' in content and '年' in content and '月' in content for content in batch_contents)
        
        # 构建内容部分
        content_sections = []
        for i, content in enumerate(batch_contents):
            content_sections.append(f"### [{i+1}]\n{content}")
        
        combined_content = "\n\n".join(content_sections)
        
        if has_timeline:
            # 时间线感知的批量prompt
            prompt = f"""
请仔细分析以下 {len(batch_contents)} 段按时间顺序排列的文本内容，构建用户的心理认知图谱。
**🕒 重要：请充分利用时间信息来判断因果关系和发展轨迹。**

{f"上下文信息：{context}" if context else ""}

分析内容（包含时间标记）：
{combined_content}

请按照以下JSON格式输出分析结果，**务必**为每段内容分别输出结果：

{{
    "batches": [
        {{
            "batch_index": 1,
            "nodes": [
                {{
                    "id": "node_1_1",
                    "type": "experience|belief|emotion|topic",
                    "content": "节点内容描述",
                    "weight": 0.8,
                    "metadata": {{
                        "confidence": 0.85,
                        "keywords": ["关键词1", "关键词2"],
                        "temporal_evidence": "基于时间线的证据",
                        "time_period": "2022年3月",
                        "source_index": 1
                    }}
                }}
            ],
            "edges": [
                {{
                    "source": "node_1_1",
                    "target": "node_1_2", 
                    "relation": "causes|influences|conflicts|supports|triggers|correlates",
                    "weight": 0.9,  // 时间证据支撑的关系可以给更高权重
                    "evidence": "时间线证据：早期经历导致后期认知"
                }}
            ]
        }}
    ]
}}

**🎯 时间线批量分析要求：**
1. **时间因果识别**：优先识别基于时间先后的因果关系
2. **发展轨迹追踪**：关注用户的成长变化轨迹
3. **权重时间调整**：为时间证据明确的关系给更高权重（0.8-0.9）
4. **时间标记利用**：充分利用【年月日】时间标记
5. **时间连接词识别**：注意"后来"、"现在"、"当时"、"之前"等

**⚡ 时间线关系模式：**
- 早期经历 → 后期观念 (LEADS_TO, weight: 0.9)
- 过去情绪 → 现在认知 (LEADS_TO, weight: 0.9)
- 以前选择 → 现在结果 (CAUSES, weight: 0.8)

【节点类型定义（时间线版）】：

**experience (经历体验)** - 具体的时间相关经历：
- 时间标记的经历："【2022年】开始创业"
- 发展过程："坚持了7年"、"经过反复尝试"

**belief (信念价值观)** - 经历后形成的观念：
- 后期感悟："后来我明白了..."、"现在我认为..."
- 发展观念："逐渐意识到..."

**emotion (情绪感受)** - 时间相关的情绪变化：
- 情绪变化："当时很兴奋"、"现在很迷茫"

**topic (话题主题)** - 持续关注的主题：
- 长期话题："一直在思考的问题"

【分类要求】：
1. 优先构建时间线关系，每段至少1-2条时间关系
2. 节点权重反映时间重要性（近期高，远期低）
3. 在metadata中添加source_index和temporal_evidence字段
4. 明确区分experience（具体经历）和belief（观点价值观）

【质量要求】：
1. 节点内容具体明确，权重在0-1之间
2. 关系有明确的时间线文本证据支撑
3. 输出有效的JSON格式
4. 每段内容最多输出{max_nodes_per_text}个节点
5. 确保有足够的时间线关系

请只返回JSON，不要包含其他内容。
"""
        else:
            # 普通的批量prompt
            prompt = f"""
请仔细分析以下 {len(batch_contents)} 段文本内容，提取用户的心理认知图谱元素。特别注意正确识别节点类型。

{f"上下文信息：{context}" if context else ""}

分析内容：
{combined_content}

请按照以下JSON格式输出分析结果，**务必**为每段内容分别输出结果：

{{
    "batches": [
        {{
            "batch_index": 1,
            "nodes": [
                {{
                    "id": "node_1_1",
                    "type": "experience|belief|emotion|topic",
                    "content": "节点内容描述",
                    "weight": 0.8,
                    "metadata": {{
                        "confidence": 0.85,
                        "keywords": ["关键词1", "关键词2"],
                        "intensity": "high|medium|low",
                        "source_index": 1
                    }}
                }}
            ],
            "edges": [
                {{
                    "source": "node_1_1",
                    "target": "node_1_2", 
                    "relation": "causes|influences|conflicts|supports|triggers|correlates",
                    "weight": 0.7,
                    "evidence": "支撑这种关系的文本证据"
                }}
            ]
        }}
    ]
}}

【重要】节点类型详细定义和示例：

**experience (经历体验)** - 用户的具体经历、事件、身份、行为、观察：
- 身份状态："我是计算机专业的学生"、"我在一家公司工作"
- 具体经历："参加了面试"、"和朋友吵架了"、"看到同学们准备考研"
- 行为表现："我选择了这个专业"、"我经常加班"
- 成果结果："成绩还不错"、"项目完成了"

**belief (信念价值观)** - 用户的价值观、原则、观点、判断标准：
- 价值判断："实际工作能力比学历更重要"、"诚信是最重要的品质"
- 人生原则："做人要踏实"、"要为自己的选择负责"
- 观念看法："我认为..."、"我觉得..."、"我相信..."

**emotion (情绪感受)** - 用户的情绪状态、心理感受：
- 情绪状态："很焦虑"、"很纠结"、"感到压力"、"很开心"
- 心理感受："迷茫"、"困惑"、"担心"、"期待"

**topic (话题主题)** - 讨论的具体主题、领域、问题：
- 讨论主题："考研问题"、"职业选择"、"感情关系"
- 领域范围："教育"、"工作"、"生活"

【分类要求】：
1. 优先识别experience节点 - 任何具体的经历、事件、身份、行为都应该分类为experience
2. 每个文本至少应该包含1-2个experience节点
3. 明确区分experience（具体经历）和belief（观点价值观）
4. 情绪词汇分类为emotion，抽象观点分类为belief
5. 在metadata中添加source_index字段标识来源段落

【质量要求】：
1. 节点内容具体明确，权重在0-1之间
2. 关系有明确的文本证据支撑
3. 输出有效的JSON格式
4. 每段内容最多输出{max_nodes_per_text}个节点
5. 确保experience节点数量合理（通常1-3个）
"""
        
        return prompt
    
    def _parse_batch_ai_response(self, response: str, batch_contents: List[str]) -> GraphElements:
        """解析批量AI响应"""
        try:
            # 检查响应是否为空或包含错误信息
            if not response or response.strip() == "":
                logger.warning("AI响应为空，使用降级策略")
                return self._fallback_analyze_multiple_contents(batch_contents)

            # 检查是否是错误响应
            if "error" in response and "AI响应超时" in response:
                logger.warning("AI响应超时，使用降级策略")
                return self._fallback_analyze_multiple_contents(batch_contents)

            # 使用稳定的JSON解析器
            from ..utils.json_parser import parse_ai_json
            data = parse_ai_json(response)

            if not data:
                logger.warning("JSON解析失败，使用降级策略")
                return self._fallback_analyze_multiple_contents(batch_contents)

            batches_data = data.get("batches", [])

            all_nodes = []
            all_edges = []

            # 处理每个批次的结果
            for batch_data in batches_data:
                nodes_data = batch_data.get("nodes", [])
                edges_data = batch_data.get("edges", [])

                # 转换节点
                for node_data in nodes_data:
                    node = self._create_node_from_data(node_data)
                    all_nodes.append(node)

                # 转换边
                for edge_data in edges_data:
                    edge = self._create_edge_from_data(edge_data)
                    if edge:
                        all_edges.append(edge)
            
            # 按类型分类节点
            experience_nodes = [n for n in all_nodes if n.node_type == NodeType.EXPERIENCE]
            belief_nodes = [n for n in all_nodes if n.node_type == NodeType.BELIEF]
            emotion_nodes = [n for n in all_nodes if n.node_type == NodeType.EMOTION]
            topic_nodes = [n for n in all_nodes if n.node_type == NodeType.TOPIC]
            
            logger.info(f"批量AI解析完成：{len(all_nodes)} 个节点，{len(all_edges)} 条边")
            
            return GraphElements(
                experience_nodes=experience_nodes,
                belief_nodes=belief_nodes,
                emotion_nodes=emotion_nodes,
                topic_nodes=topic_nodes,
                causal_edges=all_edges
            )
            
        except Exception as e:
            logger.error(f"解析批量AI响应失败：{e}")
            logger.debug(f"原始响应内容: {response[:500]}...")  # 记录前500字符用于调试

            # 回退到降级策略
            logger.warning("JSON解析失败，使用降级策略")
            return self._fallback_analyze_multiple_contents(batch_contents)
    
    def _fix_json_response(self, response: str) -> str:
        """尝试修复常见的JSON格式问题"""
        try:
            # 移除可能的markdown代码块标记
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            
            # 尝试修复未闭合的引号
            lines = response.split('\n')
            fixed_lines = []
            for line in lines:
                # 计算引号数量，如果奇数则添加引号
                quote_count = line.count('"')
                if quote_count % 2 == 1:
                    line += '"'
                fixed_lines.append(line)
            
            return '\n'.join(fixed_lines)
        except:
            return response
    
    async def _fallback_analyze_batch(self, batch_contents: List[str]) -> GraphElements:
        """批次分析的降级策略：逐个分析后合并"""
        logger.info(f"使用降级策略逐个分析 {len(batch_contents)} 段内容")
        
        tasks = []
        for content in batch_contents:
            task = self.analyze_content(content)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        valid_results = []
        for result in results:
            if not isinstance(result, Exception) and result is not None:
                valid_results.append(result)
        
        return self.merge_graph_elements(valid_results)
    
    def _fallback_analyze_multiple_contents(self, contents: List[str]) -> GraphElements:
        """多内容分析的最终降级策略"""
        logger.warning(f"使用最终降级策略分析 {len(contents)} 段内容")
        
        all_elements = []
        for content in contents[:5]:  # 限制处理数量
            elements = self._fallback_analyze_content(content)
            all_elements.append(elements)
        
        return self.merge_graph_elements(all_elements) 
#!/usr/bin/env python3
"""
测试JSON解析修复功能
"""
import asyncio
import logging
from resona.utils.json_parser import parse_ai_json

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_json_parsing():
    """测试各种JSON解析场景"""
    
    # 测试用例
    test_cases = [
        # 1. 正常JSON
        {
            "name": "正常JSON",
            "input": '{"batches": [{"nodes": [{"id": "test", "type": "topic", "content": "测试", "weight": 0.8}], "edges": []}]}',
            "should_succeed": True
        },
        
        # 2. 带有thinking标签的响应
        {
            "name": "Thinking模式",
            "input": '<think>这是思考过程</think>{"batches": [{"nodes": [{"id": "test", "type": "topic", "content": "测试", "weight": 0.8}], "edges": []}]}',
            "should_succeed": True
        },
        
        # 3. 代码块格式
        {
            "name": "代码块格式",
            "input": '```json\n{"batches": [{"nodes": [{"id": "test", "type": "topic", "content": "测试", "weight": 0.8}], "edges": []}]}\n```',
            "should_succeed": True
        },
        
        # 4. 不完整的JSON（应该失败）
        {
            "name": "不完整JSON",
            "input": '{"batches": [{"nodes": [{"id": "test", "type": "topic"',
            "should_succeed": False
        },
        
        # 5. 混合内容
        {
            "name": "混合内容",
            "input": '这是一些文本内容\n{"batches": [{"nodes": [{"id": "test", "type": "topic", "content": "测试", "weight": 0.8}], "edges": []}]}\n还有更多文本',
            "should_succeed": True
        },
        
        # 6. 实际的AI响应示例（基于错误日志）
        {
            "name": "实际AI响应",
            "input": '''[AI分析] 批次 1: 16 段内容 
 
```json 
{ 
    "batches": [ 
        { 
            "batch_index": 1, 
            "nodes": [ 
                { 
                    "id": "node_1_1", 
                    "type": "experience", 
                    "content": "用户在Cursor中遇到烦人的警告。", 
                    "weight": 0.8, 
                    "metadata": { 
                        "confidence": 0.9, 
                        "keywords": ["Cursor", "警告"], 
                        "temporal_evidence": "【2025年07月】在r/cursor评论：I also experienced this annoying warnings。", 
                        "time_period": "2025年07月", 
                        "source_index": 1 
                    } 
                }
            ],
            "edges": []
        }
    ]
}
```

[完成] 636字符 / 25.31秒 

[AI分析完成] 批次 1''',
            "should_succeed": True
        }
    ]
    
    print("开始测试JSON解析功能...\n")
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        print(f"输入长度: {len(test_case['input'])} 字符")
        
        try:
            result = parse_ai_json(test_case['input'])
            
            if result is not None:
                print("✅ 解析成功")
                print(f"结果类型: {type(result)}")
                if 'batches' in result:
                    batches = result['batches']
                    print(f"批次数量: {len(batches)}")
                    if batches:
                        nodes = batches[0].get('nodes', [])
                        print(f"第一批次节点数: {len(nodes)}")
                
                if test_case['should_succeed']:
                    success_count += 1
                    print("✅ 符合预期")
                else:
                    print("⚠️  预期失败但成功了")
            else:
                print("❌ 解析失败")
                if not test_case['should_succeed']:
                    success_count += 1
                    print("✅ 符合预期（预期失败）")
                else:
                    print("❌ 不符合预期")
                    
        except Exception as e:
            print(f"❌ 解析异常: {e}")
            if not test_case['should_succeed']:
                success_count += 1
                print("✅ 符合预期（预期异常）")
            else:
                print("❌ 不符合预期")
        
        print("-" * 50)
    
    print(f"\n测试完成: {success_count}/{total_count} 通过")
    return success_count == total_count

async def test_graph_builder():
    """测试图谱构建器的JSON解析"""
    try:
        from resona.core.graph_builder import GraphBuilder, get_ai_service
        
        print("\n测试图谱构建器...")
        
        # 创建图谱构建器
        ai_service = get_ai_service()
        builder = GraphBuilder(ai_service)
        
        # 测试响应
        test_response = '''```json 
{ 
    "batches": [ 
        { 
            "batch_index": 1, 
            "nodes": [ 
                { 
                    "id": "node_1_1", 
                    "type": "experience", 
                    "content": "用户在Cursor中遇到烦人的警告", 
                    "weight": 0.8
                }
            ],
            "edges": []
        }
    ]
}
```'''
        
        # 测试解析
        result = builder._parse_batch_ai_response(test_response, ["测试内容"])
        
        if result:
            print("✅ 图谱构建器JSON解析成功")
            all_nodes = result.get_all_nodes()
            print(f"解析得到 {len(all_nodes)} 个节点")
            if all_nodes:
                print(f"第一个节点: {all_nodes[0].content}")
            return True
        else:
            print("❌ 图谱构建器JSON解析失败")
            return False
            
    except Exception as e:
        print(f"❌ 图谱构建器测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("JSON解析修复功能测试")
    print("=" * 60)
    
    # 测试基础JSON解析
    basic_success = test_json_parsing()
    
    # 测试图谱构建器集成
    graph_success = asyncio.run(test_graph_builder())
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"基础JSON解析: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"图谱构建器集成: {'✅ 通过' if graph_success else '❌ 失败'}")
    
    if basic_success and graph_success:
        print("🎉 所有测试通过！JSON解析问题已修复。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    main()

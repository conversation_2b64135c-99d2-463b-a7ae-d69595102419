#!/usr/bin/env python3
"""
验证我们的解决方案处理的是正确的数据层次
"""

def main():
    print("=" * 60)
    print("验证JSON解析解决方案的数据层次")
    print("=" * 60)
    
    # 1. 模拟DeepInfra API的完整响应（您提供的Schema格式）
    api_response = {
        "id": "chatcmpl-example",
        "object": "chat.completion",
        "created": 1234567890,
        "model": "google/gemini-2.5-flash",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": '<think>我需要分析这些内容...</think>\n```json\n{"batches": [{"nodes": [{"id": "test", "type": "topic", "content": "测试内容"}], "edges": []}]}\n```'
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 100,
            "completion_tokens": 50,
            "total_tokens": 150
        }
    }
    
    print("1. DeepInfra API响应结构（您的Schema）:")
    print(f"   - API响应ID: {api_response['id']}")
    print(f"   - 模型: {api_response['model']}")
    print(f"   - 选择数量: {len(api_response['choices'])}")
    print(f"   - Token使用: {api_response['usage']['total_tokens']}")
    
    # 2. 提取AI生成的内容（这是问题所在的层次）
    ai_generated_content = api_response["choices"][0]["message"]["content"]
    
    print(f"\n2. AI生成的内容（choices[0].message.content）:")
    print(f"   长度: {len(ai_generated_content)} 字符")
    print(f"   内容预览: {repr(ai_generated_content[:100])}...")
    
    # 3. 使用我们的解析器处理AI生成的内容
    try:
        from resona.utils.json_parser import parse_ai_json
        
        print(f"\n3. 使用我们的JSON解析器:")
        result = parse_ai_json(ai_generated_content)
        
        if result:
            print("   ✅ 解析成功!")
            print(f"   - 批次数量: {len(result.get('batches', []))}")
            if result.get('batches'):
                nodes = result['batches'][0].get('nodes', [])
                print(f"   - 第一批次节点数: {len(nodes)}")
                if nodes:
                    print(f"   - 第一个节点内容: {nodes[0].get('content', 'N/A')}")
        else:
            print("   ❌ 解析失败")
            
    except ImportError as e:
        print(f"   ⚠️ 导入错误: {e}")
        print("   （这在测试环境中是正常的）")
    
    print(f"\n4. 数据流总结:")
    print("   HTTP请求 → DeepInfra API")
    print("   DeepInfra API → HTTP响应（您的Schema格式）")
    print("   HTTP响应.choices[0].message.content → AI生成的文本")
    print("   AI生成的文本 → 我们的JSON解析器 → 结构化数据")
    
    print(f"\n✅ 结论: 我们的解决方案处理的是正确的数据层次")
    print("   我们解析的是AI模型在message.content中生成的复杂格式文本")
    print("   而不是DeepInfra API的HTTP响应格式")

if __name__ == "__main__":
    main()

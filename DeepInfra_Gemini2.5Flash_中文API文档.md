# DeepInfra Gemini 2.5 Flash API 中文文档

## 概述

DeepInfra 提供了与 OpenAI 兼容的 HTTP API 接口，支持 Google Gemini 2.5 Flash 模型。本文档详细介绍了所有可用参数及其使用建议。

## 基本信息

- **API 端点**: `https://api.deepinfra.com/v1/openai/chat/completions`
- **模型名称**: `google/gemini-2.5-flash`
- **认证方式**: Bearer <PERSON>ken
- **兼容性**: OpenAI Chat Completions API

## 核心参数

### 必需参数

#### `model` (string)
- **描述**: 模型名称
- **值**: `"google/gemini-2.5-flash"`
- **示例**: `"model": "google/gemini-2.5-flash"`

#### `messages` (array)
- **描述**: 对话消息数组，支持 system、user、assistant、tool 角色
- **格式**: 每个消息包含 `role` 和 `content` 字段
- **使用建议**: 
  - 可在任意位置包含一个 system 消息
  - 按 (user, assistant, tool)* 模式组织对话
  - 建议在开头使用 system 消息设定角色和行为

### 输出控制参数

#### `stream` (boolean)
- **描述**: 是否启用流式输出
- **默认值**: `false`
- **使用建议**: 
  - 长文本生成时建议启用流式输出
  - 实时交互场景下提升用户体验

#### `max_tokens` (integer)
- **描述**: 生成的最大 token 数量
- **范围**: 0 ≤ max_tokens ≤ 1,000,000
- **使用建议**: 
  - 根据需求设置合理上限
  - 避免设置过大导致不必要的成本

#### `stop` (string | array)
- **描述**: 停止生成的序列，最多16个
- **使用建议**: 
  - 用于精确控制输出结束点
  - 可设置多个停止词提高控制精度

#### `n` (integer)
- **描述**: 返回的候选响应数量
- **默认值**: 1
- **范围**: 1 ≤ n ≤ 4
- **使用建议**: 
  - 通常使用默认值1
  - 需要多样性时可增加到2-3

### 采样控制参数

#### `temperature` (number)
- **描述**: 采样温度，控制输出随机性
- **默认值**: 1.0
- **范围**: 0 ≤ temperature ≤ 2
- **使用建议**:
  - **0.0-0.3**: 确定性任务（翻译、代码生成、事实问答）
  - **0.4-0.7**: 平衡创造性和准确性（一般对话、分析）
  - **0.8-1.2**: 创意写作、头脑风暴
  - **1.3-2.0**: 高度创意任务（诗歌、艺术创作）

#### `top_p` (number)
- **描述**: 核采样，考虑累积概率质量的 token
- **默认值**: 1.0
- **范围**: 0 < top_p ≤ 1
- **使用建议**:
  - **0.1**: 非常保守，只考虑最可能的词
  - **0.3-0.5**: 平衡质量和多样性
  - **0.8-0.95**: 增加创造性
  - **1.0**: 考虑所有可能的词
  - 通常与 temperature 二选一使用

#### `min_p` (number)
- **描述**: 最小概率阈值，相对于最可能 token 的概率
- **默认值**: 0
- **范围**: 0 ≤ min_p ≤ 1
- **使用建议**: 
  - 设置为 0.05-0.1 可过滤低质量输出
  - 与 top_p 配合使用效果更佳

#### `top_k` (integer)
- **描述**: 从最佳 k 个 token 中采样
- **默认值**: 0 (关闭)
- **范围**: 0 ≤ top_k < 1000
- **使用建议**:
  - **0**: 关闭 top_k 限制
  - **10-20**: 保守采样
  - **40-80**: 平衡采样（推荐）
  - **100+**: 更多样化输出

### 重复控制参数

#### `presence_penalty` (number)
- **描述**: 基于 token 是否出现的惩罚
- **默认值**: 0
- **范围**: -2 ≤ presence_penalty ≤ 2
- **使用建议**:
  - **0.0-0.6**: 轻微鼓励新话题
  - **0.7-1.2**: 明显减少重复
  - **1.3-2.0**: 强烈避免重复
  - 负值会鼓励重复使用已出现的词

#### `frequency_penalty` (number)
- **描述**: 基于 token 出现频率的惩罚
- **默认值**: 0
- **范围**: -2 ≤ frequency_penalty ≤ 2
- **使用建议**:
  - **0.0-0.3**: 轻微减少重复
  - **0.4-0.8**: 平衡重复控制
  - **0.9-1.5**: 强烈避免重复
  - 与 presence_penalty 配合使用

#### `repetition_penalty` (number)
- **描述**: 乘性重复惩罚（替代加性惩罚）
- **默认值**: 1.0
- **范围**: 0.01 ≤ repetition_penalty ≤ 5
- **使用建议**:
  - **1.0**: 无惩罚
  - **1.05-1.15**: 轻微惩罚重复
  - **1.2-1.5**: 明显减少重复
  - **< 1.0**: 鼓励重复

### 结构化输出参数

#### `response_format` (object)
- **描述**: 响应格式控制
- **支持格式**: `{"type": "json_object"}` 或 `{"type": "text"}`
- **使用建议**:
  - JSON 模式确保输出有效的 JSON 格式
  - 需要在 prompt 中明确要求 JSON 输出
  - 适用于结构化数据提取和 API 集成

### 工具调用参数

#### `tools` (array)
- **描述**: 模型可调用的工具列表
- **支持类型**: 目前仅支持 function
- **使用建议**: 
  - 为复杂任务提供外部工具能力
  - 函数定义需要清晰的描述和参数说明

#### `tool_choice` (string)
- **描述**: 控制工具调用行为
- **可选值**: `"none"`, `"auto"`
- **默认值**: 无工具时为 `"none"`，有工具时为 `"auto"`
- **使用建议**: 
  - `"auto"` 让模型自主决定是否调用工具
  - `"none"` 禁用工具调用

### 高级参数

#### `reasoning_effort` (string)
- **描述**: 推理模型的推理强度控制
- **可选值**: `"none"`, `"low"`, `"medium"`, `"high"`
- **使用建议**:
  - **"high"**: 复杂推理任务（数学、逻辑）
  - **"medium"**: 一般分析任务
  - **"low"**: 简单任务，追求速度
  - **"none"**: 禁用推理（如果模型支持）

#### `seed` (integer)
- **描述**: 随机数种子，用于结果复现
- **范围**: -9223372036854776000 ≤ seed < 18446744073709552000
- **使用建议**: 
  - 测试和调试时设置固定种子
  - 生产环境通常不设置以保持随机性

#### `logprobs` (boolean)
- **描述**: 是否返回 token 的对数概率
- **使用建议**: 
  - 用于分析模型的置信度
  - 调试和优化 prompt 时有用

#### `user` (string)
- **描述**: 终端用户标识符
- **使用建议**: 
  - 用于监控和滥用检测
  - 建议使用哈希值而非明文标识

### 流式输出参数

#### `stream_options` (object)
- **属性**:
  - `include_usage` (boolean): 是否包含使用统计，默认 true
  - `continuous_usage_stats` (boolean): 是否持续返回使用统计，默认 false
- **使用建议**: 
  - 启用 `include_usage` 监控 token 使用量
  - `continuous_usage_stats` 用于实时监控长文本生成

## 参数组合建议

### 创意写作
```json
{
  "temperature": 0.8,
  "top_p": 0.9,
  "presence_penalty": 0.6,
  "frequency_penalty": 0.3
}
```

### 代码生成
```json
{
  "temperature": 0.2,
  "top_p": 0.95,
  "max_tokens": 2048,
  "stop": ["```", "\n\n\n"]
}
```

### 结构化数据提取
```json
{
  "temperature": 0.1,
  "response_format": {"type": "json_object"},
  "max_tokens": 1024
}
```

### 对话聊天
```json
{
  "temperature": 0.7,
  "top_p": 0.9,
  "presence_penalty": 0.2,
  "frequency_penalty": 0.2
}
```

## 注意事项

1. **上下文限制**: Gemini 2.5 Flash 支持最大 1,048,576 tokens 的上下文窗口
2. **成本控制**: 合理设置 `max_tokens` 避免不必要的费用
3. **JSON 模式**: 使用 `response_format` 时需要在 prompt 中明确要求 JSON 输出
4. **工具调用**: 函数参数可能包含模型幻觉的内容，需要验证
5. **流式输出**: 适合长文本生成，但会增加请求复杂度

## 错误处理

- **500 错误**: 通常是推理异常，可以重试或调整参数
- **超时**: 复杂任务可能需要更长时间，考虑使用流式输出
- **Token 限制**: 注意输入和输出 token 总数不能超过模型限制

## 最佳实践

1. **渐进调优**: 从默认参数开始，逐步调整
2. **A/B 测试**: 对比不同参数组合的效果
3. **监控使用量**: 跟踪 token 消耗和成本
4. **缓存结果**: 对于相同输入考虑缓存响应
5. **错误重试**: 实现指数退避的重试机制

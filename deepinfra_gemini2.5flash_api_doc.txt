OpenAI-compatible HTTP API
You can POST to our OpenAI Chat Completions compatible endpoint.

Simple messages and prompts
Given a list of messages from a conversation, the model will return a response.

curl "https://api.deepinfra.com/v1/openai/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer OCDjfGAZ0AZDutH2kMU931bbnS5XrLK9" \
  -d '{
      "model": "google/gemini-2.5-flash",
      "messages": [
        {
          "role": "user",
          "content": "Hello!"
        }
      ]
    }'
copy
To which you'd get something like:

{
    "id": "chatcmpl-guMTxWgpFf",
    "object": "chat.completion",
    "created": 1694623155,
    "model": "google/gemini-2.5-flash",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": " Hello! It's nice to meet you. Is there something I can help you with or would you like to chat for a bit?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 15,
        "completion_tokens": 16,
        "total_tokens": 31,
        "estimated_cost": 0.0000268
    }
}
copy
Conversations
To create a longer chat-like conversation you just have to add each response message and each of the user messages to every request. This way the model will have the context and will be able to provide better answers. You can tweak it even further by providing a system message.

curl "https://api.deepinfra.com/v1/openai/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer OCDjfGAZ0AZDutH2kMU931bbnS5XrLK9" \
  -d '{
      "model": "google/gemini-2.5-flash",
      "messages": [
        {
            "role": "system",
            "content": "Respond like a michelin starred chef."
        },
        {
          "role": "user",
          "content": "Can you name at least two different techniques to cook lamb?"
        },
        {
          "role": "assistant",
          "content": "Bonjour! Let me tell you, my friend, cooking lamb is an art form, and I'"'"'m more than happy to share with you not two, but three of my favorite techniques to coax out the rich, unctuous flavors and tender textures of this majestic protein. First, we have the classic \"Sous Vide\" method. Next, we have the ancient art of \"Sous le Sable\". And finally, we have the more modern technique of \"Hot Smoking.\""
        },
        {
          "role": "user",
          "content": "Tell me more about the second method."
        }
      ]
    }'
copy
The conversation above might return something like the following

{
    "id": "chatcmpl-b23a3fb60cde42ce8f24bb980b4dee87",
    "object": "chat.completion",
    "created": 1715688169,
    "model": "google/gemini-2.5-flash",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Sous le Sable, my friend! It's an ancient technique that's been used for centuries in the Middle East and North Africa. The name itself..."
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 149,
        "total_tokens": 487,
        "completion_tokens": 338,
        "estimated_cost": 0.00035493
    }
}
copy
The longer the conversation gets, the more time it takes the model to generate the response. The number of messages that you can have in a conversation is limited by the context size of a model. Larger models also usually take more time to respond.


Streaming
You can turn any of the requests above into a streaming request by passing "stream": true:

curl "https://api.deepinfra.com/v1/openai/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer OCDjfGAZ0AZDutH2kMU931bbnS5XrLK9" \
  -d '{
      "model": "google/gemini-2.5-flash",
      "stream": true,
      "messages": [
        {
          "role": "user",
          "content": "Hello!"
        }
      ]
    }'
copy
to which you'd get a sequence of SSE events, finishing with [DONE].

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {"role": "assistant", "content": " "}, "finish_reason": null}]}

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {"role": "assistant", "content": " Hi"}, "finish_reason": null}]}

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {"role": "assistant", "content": "!"}, "finish_reason": null}]}

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {"role": "assistant", "content": ""}, "finish_reason": null}]}

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {"role": "assistant", "content": "</s>"}, "finish_reason": null}]}

data: {"id": "Rc5hsIPHOSfMP3rNSFUw9tfR", "object": "chat.completion.chunk", "created": 1694623354, "model": "google/gemini-2.5-flash", "choices": [{"index": 0, "delta": {}, "finish_reason": "stop"}]}

data: [DONE]
Input fields
modelstring
model name

messagesarray
conversation messages: (user,assistant,tool)*,user including one system message anywhere

streamboolean
whether to stream the output via SSE or return the full response

Default value: false

temperaturenumber
What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic

Default value: 1

Range: 0 ≤ temperature ≤ 2

top_pnumber
An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.

Default value: 1

Range: 0 < top_p ≤ 1

min_pnumber
Float that represents the minimum probability for a token to be considered, relative to the probability of the most likely token. Must be in [0, 1]. Set to 0 to disable this.

Default value: 0

Range: 0 ≤ min_p ≤ 1

top_kinteger
Sample from the best k (number of) tokens. 0 means off

Default value: 0

Range: 0 ≤ top_k < 1000

max_tokensinteger
The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length. If explicitly set to None it will be the model's max context length minus input length or 16384, whichever is smaller.

Range: 0 ≤ max_tokens ≤ 1000000

stopstring
up to 16 sequences where the API will stop generating further tokens

ninteger
number of sequences to return

Default value: 1

Range: 1 ≤ n ≤ 4

presence_penaltynumber
Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.

Default value: 0

Range: -2 ≤ presence_penalty ≤ 2

frequency_penaltynumber
Positive values penalize new tokens based on how many times they appear in the text so far, increasing the model's likelihood to talk about new topics.

Default value: 0

Range: -2 ≤ frequency_penalty ≤ 2

toolsarray
A list of tools the model may call. Currently, only functions are supported as a tool.

tool_choicestring
Controls which (if any) function is called by the model. none means the model will not call a function and instead generates a message. auto means the model can pick between generating a message or calling a function. specifying a particular function choice is not supported currently.none is the default when no functions are present. auto is the default if functions are present.

response_formatobject
The format of the response. Currently, only json is supported.

repetition_penaltynumber
Alternative penalty for repetition, but multiplicative instead of additive (> 1 penalize, < 1 encourage)

Default value: 1

Range: 0.01 ≤ repetition_penalty ≤ 5

userstring
A unique identifier representing your end-user, which can help monitor and detect abuse. Avoid sending us any identifying information. We recommend hashing user identifiers.

seedinteger
Seed for random number generator. If not provided, a random seed is used. Determinism is not guaranteed.

Range: -9223372036854776000 ≤ seed < 18446744073709552000

logprobsboolean
Whether to return log probabilities of the output tokens or not.If true, returns the log probabilities of each output token returned in the `content` of `message`.

stream_optionsobject
streaming options

reasoning_effortstring
Constrains effort on reasoning for reasoning models. Currently supported values are none, low, medium, and high. Reducing reasoning effort can result in faster responses and fewer tokens used on reasoning in a response. Setting to none disables reasoning entirely if the model supports.

Allowed values: lowmediumhighnone

Input Schema
This is the detailed description of the input parameters in JSON Schema format

{
    "definitions": {
        "ChatCompletionAssistantMessage": {
            "properties": {
                "role": {
                    "const": "assistant",
                    "default": "assistant",
                    "description": "the role of the author of this message",
                    "title": "Role",
                    "type": "string"
                },
                "content": {
                    "description": "the message content",
                    "title": "Content",
                    "type": "string"
                },
                "name": {
                    "title": "Name",
                    "type": "string"
                },
                "tool_calls": {
                    "description": "the tool calls generated by the mode",
                    "items": {
                        "$ref": "#/definitions/ChatCompletionMessageToolCall"
                    },
                    "title": "Tool Calls",
                    "type": "array"
                }
            },
            "title": "ChatCompletionAssistantMessage",
            "type": "object"
        },
        "ChatCompletionContentPartAudio": {
            "properties": {
                "type": {
                    "const": "input_audio",
                    "title": "Type",
                    "type": "string"
                },
                "input_audio": {
                    "$ref": "#/definitions/InputAudio"
                }
            },
            "required": [
                "type",
                "input_audio"
            ],
            "title": "ChatCompletionContentPartAudio",
            "type": "object"
        },
        "ChatCompletionContentPartImage": {
            "properties": {
                "type": {
                    "const": "image_url",
                    "title": "Type",
                    "type": "string"
                },
                "image_url": {
                    "$ref": "#/definitions/ImageURL"
                }
            },
            "required": [
                "type",
                "image_url"
            ],
            "title": "ChatCompletionContentPartImage",
            "type": "object"
        },
        "ChatCompletionContentPartText": {
            "properties": {
                "type": {
                    "const": "text",
                    "title": "Type",
                    "type": "string"
                },
                "text": {
                    "title": "Text",
                    "type": "string"
                }
            },
            "required": [
                "type",
                "text"
            ],
            "title": "ChatCompletionContentPartText",
            "type": "object"
        },
        "ChatCompletionMessageToolCall": {
            "properties": {
                "id": {
                    "description": "the id of the tool call",
                    "title": "Id",
                    "type": "string"
                },
                "type": {
                    "description": "the type of the tool call. only function is supported currently",
                    "title": "Type",
                    "type": "string"
                },
                "function": {
                    "$ref": "#/definitions/Function",
                    "description": "the function that the model called"
                }
            },
            "required": [
                "id",
                "type",
                "function"
            ],
            "title": "ChatCompletionMessageToolCall",
            "type": "object"
        },
        "ChatCompletionSystemMessage": {
            "properties": {
                "role": {
                    "const": "system",
                    "default": "system",
                    "description": "the role of the author of this message",
                    "title": "Role",
                    "type": "string"
                },
                "content": {
                    "anyOf": [
                        {
                            "type": "string"
                        },
                        {
                            "items": {
                                "$ref": "#/definitions/ChatCompletionContentPartText"
                            },
                            "type": "array"
                        }
                    ],
                    "description": "the message content",
                    "title": "Content"
                },
                "name": {
                    "title": "Name",
                    "type": "string"
                }
            },
            "required": [
                "content"
            ],
            "title": "ChatCompletionSystemMessage",
            "type": "object"
        },
        "ChatCompletionToolMessage": {
            "properties": {
                "role": {
                    "const": "tool",
                    "default": "tool",
                    "description": "the role of the author of this message",
                    "title": "Role",
                    "type": "string"
                },
                "content": {
                    "description": "the message content",
                    "title": "Content",
                    "type": "string"
                },
                "tool_call_id": {
                    "title": "Tool Call Id",
                    "type": "string"
                }
            },
            "required": [
                "content",
                "tool_call_id"
            ],
            "title": "ChatCompletionToolMessage",
            "type": "object"
        },
        "ChatCompletionUserMessage": {
            "properties": {
                "role": {
                    "const": "user",
                    "default": "user",
                    "description": "the role of the author of this message",
                    "title": "Role",
                    "type": "string"
                },
                "content": {
                    "anyOf": [
                        {
                            "type": "string"
                        },
                        {
                            "items": {
                                "anyOf": [
                                    {
                                        "$ref": "#/definitions/ChatCompletionContentPartText"
                                    },
                                    {
                                        "$ref": "#/definitions/ChatCompletionContentPartImage"
                                    },
                                    {
                                        "$ref": "#/definitions/ChatCompletionContentPartAudio"
                                    }
                                ]
                            },
                            "type": "array"
                        }
                    ],
                    "description": "the message content",
                    "title": "Content"
                },
                "name": {
                    "title": "Name",
                    "type": "string"
                }
            },
            "required": [
                "content"
            ],
            "title": "ChatCompletionUserMessage",
            "type": "object"
        },
        "ChatTools": {
            "properties": {
                "type": {
                    "default": "function",
                    "title": "Type",
                    "type": "string"
                },
                "function": {
                    "$ref": "#/definitions/FunctionDefinition"
                }
            },
            "required": [
                "function"
            ],
            "title": "ChatTools",
            "type": "object"
        },
        "Function": {
            "properties": {
                "name": {
                    "description": "the name of the function to call",
                    "title": "Name",
                    "type": "string"
                },
                "arguments": {
                    "description": "the function arguments, generated by the model in JSON format. the model does not always generate valid JSON, and may hallucinate parameters not defined by your function schema",
                    "title": "Arguments",
                    "type": "string"
                }
            },
            "required": [
                "name",
                "arguments"
            ],
            "title": "Function",
            "type": "object"
        },
        "FunctionDefinition": {
            "properties": {
                "name": {
                    "title": "Name",
                    "type": "string"
                },
                "description": {
                    "title": "Description",
                    "type": "string"
                },
                "parameters": {
                    "title": "Parameters",
                    "type": "object"
                }
            },
            "required": [
                "name"
            ],
            "title": "FunctionDefinition",
            "type": "object"
        },
        "ImageURL": {
            "properties": {
                "url": {
                    "title": "Url",
                    "type": "string"
                },
                "detail": {
                    "default": "auto",
                    "enum": [
                        "auto",
                        "low",
                        "high"
                    ],
                    "title": "Detail",
                    "type": "string"
                }
            },
            "required": [
                "url"
            ],
            "title": "ImageURL",
            "type": "object"
        },
        "InputAudio": {
            "properties": {
                "data": {
                    "title": "Data",
                    "type": "string"
                },
                "format": {
                    "default": "wav",
                    "enum": [
                        "wav",
                        "mp3"
                    ],
                    "title": "Format",
                    "type": "string"
                }
            },
            "required": [
                "data"
            ],
            "title": "InputAudio",
            "type": "object"
        },
        "ResponseFormat": {
            "properties": {
                "type": {
                    "default": "text",
                    "enum": [
                        "text",
                        "json_object"
                    ],
                    "title": "Type",
                    "type": "string"
                }
            },
            "title": "ResponseFormat",
            "type": "object"
        },
        "StreamOptions": {
            "properties": {
                "include_usage": {
                    "default": true,
                    "description": "whether to include usage data",
                    "title": "Include Usage",
                    "type": "boolean"
                },
                "continuous_usage_stats": {
                    "default": false,
                    "description": "whether to include usage stats continuously with each streaming event",
                    "title": "Continuous Usage Stats",
                    "type": "boolean"
                }
            },
            "title": "StreamOptions",
            "type": "object"
        }
    },
    "required": [
        "model",
        "messages"
    ],
    "title": "OpenAIChatCompletionsIn",
    "type": "object",
    "properties": {
        "model": {
            "description": "model name",
            "title": "Model",
            "type": "string",
            "example": "meta-llama/Llama-2-70b-chat-hf"
        },
        "messages": {
            "description": "conversation messages: (user,assistant,tool)*,user including one system message anywhere",
            "items": {
                "anyOf": [
                    {
                        "$ref": "#/definitions/ChatCompletionToolMessage"
                    },
                    {
                        "$ref": "#/definitions/ChatCompletionAssistantMessage"
                    },
                    {
                        "$ref": "#/definitions/ChatCompletionUserMessage"
                    },
                    {
                        "$ref": "#/definitions/ChatCompletionSystemMessage"
                    }
                ]
            },
            "title": "Messages",
            "type": "array"
        },
        "stream": {
            "default": false,
            "description": "whether to stream the output via SSE or return the full response",
            "title": "Stream",
            "type": "boolean"
        },
        "temperature": {
            "default": 1,
            "description": "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic",
            "maximum": 2,
            "minimum": 0,
            "title": "Temperature",
            "type": "number"
        },
        "top_p": {
            "default": 1,
            "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.",
            "exclusiveMinimum": 0,
            "maximum": 1,
            "title": "Top P",
            "type": "number"
        },
        "min_p": {
            "default": 0,
            "description": "Float that represents the minimum probability for a token to be considered, relative to the probability of the most likely token. Must be in [0, 1]. Set to 0 to disable this.",
            "maximum": 1,
            "minimum": 0,
            "title": "Min P",
            "type": "number"
        },
        "top_k": {
            "default": 0,
            "description": "Sample from the best k (number of) tokens. 0 means off",
            "exclusiveMaximum": 1000,
            "minimum": 0,
            "title": "Top K",
            "type": "integer"
        },
        "max_tokens": {
            "description": "The maximum number of tokens to generate in the chat completion.\n\nThe total length of input tokens and generated tokens is limited by the model's context length. If explicitly set to None it will be the model's max context length minus input length or 16384, whichever is smaller.",
            "maximum": 1000000,
            "minimum": 0,
            "title": "Max Tokens",
            "type": "integer"
        },
        "stop": {
            "anyOf": [
                {
                    "type": "string"
                },
                {
                    "items": {
                        "type": "string"
                    },
                    "type": "array"
                }
            ],
            "description": "up to 16 sequences where the API will stop generating further tokens",
            "title": "Stop"
        },
        "n": {
            "default": 1,
            "description": "number of sequences to return",
            "maximum": 4,
            "minimum": 1,
            "title": "N",
            "type": "integer"
        },
        "presence_penalty": {
            "default": 0,
            "description": "Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.",
            "maximum": 2,
            "minimum": -2,
            "title": "Presence Penalty",
            "type": "number"
        },
        "frequency_penalty": {
            "default": 0,
            "description": "Positive values penalize new tokens based on how many times they appear in the text so far, increasing the model's likelihood to talk about new topics.",
            "maximum": 2,
            "minimum": -2,
            "title": "Frequency Penalty",
            "type": "number"
        },
        "tools": {
            "description": "A list of tools the model may call. Currently, only functions are supported as a tool.",
            "items": {
                "$ref": "#/definitions/ChatTools"
            },
            "title": "Tools",
            "type": "array"
        },
        "tool_choice": {
            "description": "Controls which (if any) function is called by the model. none means the model will not call a function and instead generates a message. auto means the model can pick between generating a message or calling a function. specifying a particular function choice is not supported currently.none is the default when no functions are present. auto is the default if functions are present.",
            "title": "Tool Choice",
            "type": "string"
        },
        "response_format": {
            "$ref": "#/definitions/ResponseFormat",
            "description": "The format of the response. Currently, only json is supported.",
            "type": "object",
            "title": "ResponseFormat"
        },
        "repetition_penalty": {
            "default": 1,
            "description": "Alternative penalty for repetition, but multiplicative instead of additive (> 1 penalize, < 1 encourage)",
            "maximum": 5,
            "minimum": 0.01,
            "title": "Repetition Penalty",
            "type": "number"
        },
        "user": {
            "description": "A unique identifier representing your end-user, which can help monitor and detect abuse. Avoid sending us any identifying information. We recommend hashing user identifiers.",
            "title": "User",
            "type": "string"
        },
        "seed": {
            "description": "Seed for random number generator. If not provided, a random seed is used. Determinism is not guaranteed.",
            "exclusiveMaximum": 18446744073709552000,
            "minimum": -9223372036854776000,
            "title": "Seed",
            "type": "integer"
        },
        "logprobs": {
            "description": "Whether to return log probabilities of the output tokens or not.If true, returns the log probabilities of each output token returned in the `content` of `message`.",
            "title": "Logprobs",
            "type": "boolean"
        },
        "stream_options": {
            "$ref": "#/definitions/StreamOptions",
            "description": "streaming options",
            "type": "object",
            "title": "StreamOptions"
        },
        "reasoning_effort": {
            "description": "Constrains effort on reasoning for reasoning models. Currently supported values are none, low, medium, and high. Reducing reasoning effort can result in faster responses and fewer tokens used on reasoning in a response. Setting to none disables reasoning entirely if the model supports.",
            "enum": [
                "low",
                "medium",
                "high",
                "none"
            ],
            "title": "Reasoning Effort",
            "type": "string"
        }
    }
}
Output Schema
This is the detailed description of the output parameters in JSON Schema format

{
    "definitions": {
        "ChatCompletionAssistantMessage": {
            "properties": {
                "role": {
                    "const": "assistant",
                    "default": "assistant",
                    "description": "the role of the author of this message",
                    "title": "Role",
                    "type": "string"
                },
                "content": {
                    "description": "the message content",
                    "title": "Content",
                    "type": "string"
                },
                "name": {
                    "title": "Name",
                    "type": "string"
                },
                "tool_calls": {
                    "description": "the tool calls generated by the mode",
                    "items": {
                        "$ref": "#/definitions/ChatCompletionMessageToolCall"
                    },
                    "title": "Tool Calls",
                    "type": "array"
                }
            },
            "title": "ChatCompletionAssistantMessage",
            "type": "object"
        },
        "ChatCompletionMessageToolCall": {
            "properties": {
                "id": {
                    "description": "the id of the tool call",
                    "title": "Id",
                    "type": "string"
                },
                "type": {
                    "description": "the type of the tool call. only function is supported currently",
                    "title": "Type",
                    "type": "string"
                },
                "function": {
                    "$ref": "#/definitions/Function",
                    "description": "the function that the model called"
                }
            },
            "required": [
                "id",
                "type",
                "function"
            ],
            "title": "ChatCompletionMessageToolCall",
            "type": "object"
        },
        "FinishReason": {
            "enum": [
                "stop",
                "length",
                "tool_calls"
            ],
            "title": "FinishReason",
            "type": "string"
        },
        "Function": {
            "properties": {
                "name": {
                    "description": "the name of the function to call",
                    "title": "Name",
                    "type": "string"
                },
                "arguments": {
                    "description": "the function arguments, generated by the model in JSON format. the model does not always generate valid JSON, and may hallucinate parameters not defined by your function schema",
                    "title": "Arguments",
                    "type": "string"
                }
            },
            "required": [
                "name",
                "arguments"
            ],
            "title": "Function",
            "type": "object"
        },
        "OpenAIChatCompletionChoice": {
            "properties": {
                "index": {
                    "description": "index of the choice in th list of choices",
                    "title": "Index",
                    "type": "integer"
                },
                "message": {
                    "$ref": "#/definitions/ChatCompletionAssistantMessage",
                    "description": "a chat completion message generated by the model"
                },
                "finish_reason": {
                    "$ref": "#/definitions/FinishReason",
                    "description": "the reason the model stopped generating tokens. stop if the model hit a natural stop point or a provided stop sequence, length if the maximum number of tokens specified in the request was reached, tool_calls if the model called a tool."
                },
                "logprobs": {
                    "$ref": "#/definitions/OpenAIChoiceLogProbs",
                    "description": "Log probability information for the choice."
                }
            },
            "required": [
                "index",
                "message"
            ],
            "title": "OpenAIChatCompletionChoice",
            "type": "object"
        },
        "OpenAIChatCompletionTokenLogprob": {
            "properties": {
                "token": {
                    "description": "The token.",
                    "title": "Token",
                    "type": "string"
                },
                "bytes": {
                    "description": "A list of integers representing the UTF-8 bytes representation of the token.",
                    "items": {
                        "type": "integer"
                    },
                    "title": "Bytes",
                    "type": "array"
                },
                "logprob": {
                    "description": "the log probability of the token",
                    "title": "Logprob",
                    "type": "number"
                }
            },
            "required": [
                "token",
                "logprob"
            ],
            "title": "OpenAIChatCompletionTokenLogprob",
            "type": "object"
        },
        "OpenAIChoiceLogProbs": {
            "properties": {
                "content": {
                    "description": "A list of message content tokens with log probability information.",
                    "items": {
                        "$ref": "#/definitions/OpenAIChatCompletionTokenLogprob"
                    },
                    "title": "Content",
                    "type": "array"
                }
            },
            "title": "OpenAIChoiceLogProbs",
            "type": "object"
        },
        "UsageInfo": {
            "properties": {
                "prompt_tokens": {
                    "default": 0,
                    "description": "number of tokens in the prompt",
                    "title": "Prompt Tokens",
                    "type": "integer"
                },
                "total_tokens": {
                    "default": 0,
                    "description": "total number of tokens in the completion (prompt + completion)",
                    "title": "Total Tokens",
                    "type": "integer"
                },
                "completion_tokens": {
                    "default": 0,
                    "description": "number of tokens generated in the completion",
                    "title": "Completion Tokens",
                    "type": "integer"
                },
                "estimated_cost": {
                    "description": "estimated cost of the completion in USD",
                    "title": "Estimated Cost",
                    "type": "number"
                }
            },
            "title": "UsageInfo",
            "type": "object"
        }
    },
    "required": [
        "model",
        "choices",
        "usage"
    ],
    "title": "OpenAIChatCompletionOut",
    "type": "object",
    "properties": {
        "id": {
            "description": "a unique identifier for the completion",
            "title": "Id",
            "type": "string"
        },
        "object": {
            "default": "chat.completion",
            "description": "the object type, which is always chat.completion",
            "title": "Object",
            "type": "string"
        },
        "created": {
            "description": "unix timestamp of the completion",
            "title": "Created",
            "type": "integer"
        },
        "model": {
            "description": "model name",
            "title": "Model",
            "type": "string",
            "example": "meta-llama/Llama-2-70b-chat-hf"
        },
        "choices": {
            "description": "a list of chat completion choices, can be more than one",
            "items": {
                "$ref": "#/definitions/OpenAIChatCompletionChoice"
            },
            "title": "Choices",
            "type": "array"
        },
        "usage": {
            "$ref": "#/definitions/UsageInfo",
            "description": "usage data for the completion request",
            "type": "object",
            "title": "UsageInfo"
        }
    }
}
Streaming Schema
This is the detailed description of the output stream parameters in JSON Schema format

{
    "definitions": {
        "ChatMessageRole": {
            "enum": [
                "system",
                "user",
                "assistant",
                "tool"
            ],
            "title": "ChatMessageRole",
            "type": "string"
        },
        "FinishReason": {
            "enum": [
                "stop",
                "length",
                "tool_calls"
            ],
            "title": "FinishReason",
            "type": "string"
        },
        "OpenAIChatCompletionStreamChoice": {
            "properties": {
                "index": {
                    "description": "index of the choice in th list of choices",
                    "title": "Index",
                    "type": "integer"
                },
                "delta": {
                    "$ref": "#/definitions/OpenAIDeltaMessage",
                    "description": "a chat completion delta generated by streamed model responses"
                },
                "finish_reason": {
                    "$ref": "#/definitions/FinishReason",
                    "description": "the reason the model stopped generating tokens. stop if the model hit a natural stop point or a provided stop sequence, length if the maximum number of tokens specified in the request was reached, tool_calls if the model called a tool."
                }
            },
            "required": [
                "index",
                "delta"
            ],
            "title": "OpenAIChatCompletionStreamChoice",
            "type": "object"
        },
        "OpenAIChatCompletionTokenLogprob": {
            "properties": {
                "token": {
                    "description": "The token.",
                    "title": "Token",
                    "type": "string"
                },
                "bytes": {
                    "description": "A list of integers representing the UTF-8 bytes representation of the token.",
                    "items": {
                        "type": "integer"
                    },
                    "title": "Bytes",
                    "type": "array"
                },
                "logprob": {
                    "description": "the log probability of the token",
                    "title": "Logprob",
                    "type": "number"
                }
            },
            "required": [
                "token",
                "logprob"
            ],
            "title": "OpenAIChatCompletionTokenLogprob",
            "type": "object"
        },
        "OpenAIChoiceLogProbs": {
            "properties": {
                "content": {
                    "description": "A list of message content tokens with log probability information.",
                    "items": {
                        "$ref": "#/definitions/OpenAIChatCompletionTokenLogprob"
                    },
                    "title": "Content",
                    "type": "array"
                }
            },
            "title": "OpenAIChoiceLogProbs",
            "type": "object"
        },
        "OpenAIDeltaMessage": {
            "properties": {
                "role": {
                    "$ref": "#/definitions/ChatMessageRole",
                    "description": "the role of the author of this message"
                },
                "content": {
                    "description": "the chunk message content",
                    "title": "Content",
                    "type": "string"
                },
                "tool_calls": {
                    "description": "tool calls generated by the model",
                    "items": {
                        "$ref": "#/definitions/OpenAIDeltaToolCall"
                    },
                    "title": "Tool Calls",
                    "type": "array"
                },
                "logprobs": {
                    "$ref": "#/definitions/OpenAIChoiceLogProbs",
                    "description": "Log probability information for the choice."
                }
            },
            "title": "OpenAIDeltaMessage",
            "type": "object"
        },
        "OpenAIDeltaToolCall": {
            "properties": {
                "index": {
                    "title": "Index",
                    "type": "integer"
                },
                "id": {
                    "title": "Id",
                    "type": "string"
                },
                "function": {
                    "$ref": "#/definitions/OpenAIDeltaToolCallFunction"
                },
                "type": {
                    "const": "function",
                    "title": "Type",
                    "type": "string"
                }
            },
            "required": [
                "index"
            ],
            "title": "OpenAIDeltaToolCall",
            "type": "object"
        },
        "OpenAIDeltaToolCallFunction": {
            "properties": {
                "arguments": {
                    "title": "Arguments",
                    "type": "string"
                },
                "name": {
                    "title": "Name",
                    "type": "string"
                }
            },
            "title": "OpenAIDeltaToolCallFunction",
            "type": "object"
        },
        "UsageInfo": {
            "properties": {
                "prompt_tokens": {
                    "default": 0,
                    "description": "number of tokens in the prompt",
                    "title": "Prompt Tokens",
                    "type": "integer"
                },
                "total_tokens": {
                    "default": 0,
                    "description": "total number of tokens in the completion (prompt + completion)",
                    "title": "Total Tokens",
                    "type": "integer"
                },
                "completion_tokens": {
                    "default": 0,
                    "description": "number of tokens generated in the completion",
                    "title": "Completion Tokens",
                    "type": "integer"
                },
                "estimated_cost": {
                    "description": "estimated cost of the completion in USD",
                    "title": "Estimated Cost",
                    "type": "number"
                }
            },
            "title": "UsageInfo",
            "type": "object"
        }
    },
    "required": [
        "model",
        "choices"
    ],
    "title": "OpenAIChatCompletionStreamOut",
    "type": "object",
    "properties": {
        "id": {
            "description": "a unique identifier for the completion",
            "title": "Id",
            "type": "string"
        },
        "object": {
            "default": "chat.completion.chunk",
            "description": "the object type, which is always chat.completion.chunk",
            "title": "Object",
            "type": "string"
        },
        "created": {
            "description": "unix timestamp of the completion",
            "title": "Created",
            "type": "integer"
        },
        "model": {
            "description": "model name",
            "title": "Model",
            "type": "string",
            "example": "meta-llama/Llama-2-70b-chat-hf"
        },
        "choices": {
            "description": "a list of chat completion choices, can be more than one",
            "items": {
                "$ref": "#/definitions/OpenAIChatCompletionStreamChoice"
            },
            "title": "Choices",
            "type": "array"
        },
        "usage": {
            "$ref": "#/definitions/UsageInfo",
            "description": "usage data about request and response",
            "type": "object",
            "title": "UsageInfo"
        }
    }
}
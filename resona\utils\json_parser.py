"""
稳定的JSON解析工具 - 专门处理AI响应中的JSON提取和修复
"""
import json
import re
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)


class StableJSONParser:
    """稳定的JSON解析器，专门处理AI模型的响应"""
    
    def __init__(self):
        """初始化JSON解析器"""
        self.extraction_strategies = [
            self._extract_json_object,
            self._extract_from_code_blocks,
            self._extract_after_think_tags,
            self._extract_with_regex_patterns,
            self._extract_with_repair,
            self._extract_partial_json
        ]
    
    def parse_ai_response(self, response: str) -> Optional[Dict[str, Any]]:
        """
        从AI响应中解析JSON
        
        Args:
            response: AI模型的原始响应
            
        Returns:
            解析后的JSON对象，如果失败返回None
        """
        if not response or not response.strip():
            logger.warning("响应为空")
            return None
        
        response = response.strip()
        logger.debug(f"开始解析响应，长度: {len(response)}")
        
        # 依次尝试各种提取策略
        for i, strategy in enumerate(self.extraction_strategies):
            try:
                result = strategy(response)
                if result:
                    logger.debug(f"策略 {i+1} 成功提取JSON")
                    return result
            except Exception as e:
                logger.debug(f"策略 {i+1} 失败: {e}")
                continue
        
        logger.error("所有JSON提取策略都失败")
        self._log_debug_info(response)
        return None
    
    def _extract_json_object(self, response: str) -> Optional[Dict[str, Any]]:
        """策略1: 直接解析JSON对象"""
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            return None
    
    def _extract_from_code_blocks(self, response: str) -> Optional[Dict[str, Any]]:
        """策略2: 从代码块中提取JSON"""
        patterns = [
            r'```json\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            for match in matches:
                match = match.strip()
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        return None
    
    def _extract_after_think_tags(self, response: str) -> Optional[Dict[str, Any]]:
        """策略3: 处理thinking模型的<think>标签"""
        # 方法1: 匹配 <think>...</think>{JSON} 格式
        think_pattern = r'<think>.*?</think>\s*(\{.*?\})'
        match = re.search(think_pattern, response, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass
        
        # 方法2: 提取</think>之后的内容
        think_end = response.find('</think>')
        if think_end != -1:
            after_think = response[think_end + 8:].strip()
            json_obj = self._find_complete_json_object(after_think)
            if json_obj:
                try:
                    return json.loads(json_obj)
                except json.JSONDecodeError:
                    pass
        
        # 方法3: 移除所有think标签后提取
        cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        cleaned = cleaned.strip()
        if cleaned:
            json_obj = self._find_complete_json_object(cleaned)
            if json_obj:
                try:
                    return json.loads(json_obj)
                except json.JSONDecodeError:
                    pass
        
        return None
    
    def _extract_with_regex_patterns(self, response: str) -> Optional[Dict[str, Any]]:
        """策略4: 使用正则表达式匹配JSON对象"""
        patterns = [
            r'(\{(?:[^{}]|{[^{}]*})*\})',  # 简单嵌套
            r'(\{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*\})',  # 更深嵌套
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                match = match.strip()
                if match.startswith('{') and match.endswith('}'):
                    try:
                        return json.loads(match)
                    except json.JSONDecodeError:
                        continue
        return None
    
    def _extract_with_repair(self, response: str) -> Optional[Dict[str, Any]]:
        """策略5: 智能修复常见JSON错误"""
        json_candidate = self._find_complete_json_object(response)
        if not json_candidate:
            return None
        
        # 尝试修复常见问题
        repairs = [
            # 移除末尾的逗号
            lambda s: re.sub(r',\s*}', '}', s),
            lambda s: re.sub(r',\s*]', ']', s),
            # 修复单引号为双引号
            lambda s: re.sub(r"'([^']*)':", r'"\1":', s),
            # 修复没有引号的键名
            lambda s: re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', s),
            # 修复未闭合的字符串
            lambda s: self._fix_unclosed_strings(s)
        ]
        
        for repair_func in repairs:
            try:
                repaired = repair_func(json_candidate)
                return json.loads(repaired)
            except (json.JSONDecodeError, Exception):
                continue
        
        return None
    
    def _extract_partial_json(self, response: str) -> Optional[Dict[str, Any]]:
        """策略6: 提取部分JSON作为降级方案"""
        # 查找最大的JSON片段
        max_json = ""
        max_length = 0
        
        # 查找所有可能的JSON开始位置
        for i, char in enumerate(response):
            if char == '{':
                # 尝试从这个位置开始构建JSON
                for j in range(i + 1, len(response) + 1):
                    candidate = response[i:j]
                    try:
                        json.loads(candidate)
                        if len(candidate) > max_length:
                            max_json = candidate
                            max_length = len(candidate)
                    except json.JSONDecodeError:
                        continue
        
        if max_json:
            try:
                return json.loads(max_json)
            except json.JSONDecodeError:
                pass
        
        return None
    
    def _find_complete_json_object(self, text: str) -> Optional[str]:
        """查找完整的JSON对象"""
        text = text.strip()
        if not text.startswith('{'):
            # 查找第一个{
            start = text.find('{')
            if start == -1:
                return None
            text = text[start:]
        
        # 使用括号匹配找到完整的JSON对象
        brace_count = 0
        in_string = False
        escape_next = False
        
        for i, char in enumerate(text):
            if escape_next:
                escape_next = False
                continue
            
            if char == '\\':
                escape_next = True
                continue
            
            if char == '"' and not escape_next:
                in_string = not in_string
                continue
            
            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        return text[:i + 1]
        
        return None
    
    def _fix_unclosed_strings(self, json_str: str) -> str:
        """修复未闭合的字符串"""
        lines = json_str.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 计算引号数量，如果奇数则添加引号
            quote_count = line.count('"')
            if quote_count % 2 == 1:
                line += '"'
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _log_debug_info(self, response: str):
        """记录调试信息"""
        logger.error(f"响应长度: {len(response)}")
        logger.error(f"包含花括号: {'{' in response}")
        logger.error(f"左括号数量: {response.count('{')}")
        logger.error(f"右括号数量: {response.count('}')}")
        
        if '<think>' in response:
            think_count = response.count('<think>')
            think_end_count = response.count('</think>')
            logger.error(f"Think标签统计: <think>={think_count}, </think>={think_end_count}")
        
        if len(response) > 0:
            logger.error(f"响应开头: {response[:200]}")
            logger.error(f"响应结尾: {response[-200:]}")


# 全局实例
_global_parser = StableJSONParser()


def parse_ai_json(response: str) -> Optional[Dict[str, Any]]:
    """
    便捷函数：解析AI响应中的JSON
    
    Args:
        response: AI模型的原始响应
        
    Returns:
        解析后的JSON对象，如果失败返回None
    """
    return _global_parser.parse_ai_response(response)

PS D:\pycharmproject\CogBridges_v020> & d:/pycharmproject/CogBridges_v020/.venv/Scripts/python.exe d:/pycharmproject/CogBridges_v020/only_profile/start.py
🚀 CogBridges Reddit用户画像分析启动器
==================================================

📋 环境检查：
✅ Python版本检查通过：3.12.5
✅ 核心依赖检查通过
✅ 目录结构检查完成
⚠️  .env文件不存在，使用默认配置
   建议创建.env文件并配置API密钥
✅ 配置验证通过
   - AI服务：DeepInfra
   - LLM模型：google/gemini-2.5-flash
   - Reddit配置：已配置
   - 服务器：0.0.0.0:8001

✨ 环境检查完成！

🌟 启动应用...
2025-07-07 01:19:59,231 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-07 01:20:02,827 - faiss.loader - INFO - Loading faiss with AVX2 support.
2025-07-07 01:20:02,890 - faiss.loader - INFO - Successfully loaded faiss with AVX2 support.
2025-07-07 01:20:02,906 - faiss - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-07 01:20:02,931 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-07 01:20:02,950 - app - INFO - 分析结果持久化表初始化完成
2025-07-07 01:20:02,950 - app - INFO - Reddit用户画像分析应用初始化完成

🎯 应用启动中...
   - 地址：http://127.0.0.1:5000
   - 调试模式：关闭
   - 按 Ctrl+C 停止应用
==================================================
2025-07-07 01:20:02,950 - app - INFO - 启动Reddit用户画像分析应用 - http://127.0.0.1:5000
 * Serving Flask app 'app'
 * Debug mode: off
2025-07-07 01:20:02,974 - werkzeug - INFO - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on http://127.0.0.1:5000
2025-07-07 01:20:02,974 - werkzeug - INFO - Press CTRL+C to quit
2025-07-07 01:20:22,622 - app - INFO - 开始分析Reddit链接: https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button
2025-07-07 01:20:22,622 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-07 01:20:22,622 - resona.services.reddit_service - INFO - 使用代理访问 Reddit: http://127.0.0.1:7890
2025-07-07 01:20:22,622 - data_crawler - INFO - 数据抓取器初始化完成 | 配置: {}
2025-07-07 01:20:22,622 - app - INFO - 数据抓取器初始化完成
2025-07-07 01:20:22,622 - resona.services.ai_service - INFO - 为模型 google/gemini-2.5-flash 启用 JSON 模式 (model_kwargs)
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO - AI 服务初始化完成:
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO -   - 模型: google/gemini-2.5-flash
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO -   - 描述: Google Gemini 2.5 Flash - 快速高效的对话模型
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO -   - JSON 模式: 启用
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO -   - 温度: 0.7
2025-07-07 01:20:24,521 - resona.services.ai_service - INFO -   - 最大输出: 8192
2025-07-07 01:20:25,193 - resona.core.semantic_analyzer - INFO - 语义解析器初始化完成
2025-07-07 01:20:25,193 - semantic_analyzer - INFO - 用户档案语义分析器初始化完成
2025-07-07 01:20:25,193 - app - INFO - 语义分析器初始化完成
2025-07-07 01:20:25,193 - resona.services.ai_service - INFO - 为模型 google/gemini-2.5-flash 启用 JSON 模式 (model_kwargs)
2025-07-07 01:20:26,530 - resona.services.ai_service - INFO - AI 服务初始化完成:
2025-07-07 01:20:26,530 - resona.services.ai_service - INFO -   - 模型: google/gemini-2.5-flash
2025-07-07 01:20:26,530 - resona.services.ai_service - INFO -   - 描述: Google Gemini 2.5 Flash - 快速高效的对话模型
2025-07-07 01:20:26,530 - resona.services.ai_service - INFO -   - JSON 模式: 启用
2025-07-07 01:20:26,545 - resona.services.ai_service - INFO -   - 温度: 0.7
2025-07-07 01:20:26,545 - resona.services.ai_service - INFO -   - 最大输出: 8192
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO - Token编码器初始化成功，模型: google/gemini-2.5-flash
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO - Token预算智能计算完成:
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 模型: google/gemini-2.5-flash
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 描述: Google Gemini 2.5 Flash - 快速高效的对话模型
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 上下文窗口: 1,048,576
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 预留输出: 209,715 (20.0%)
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 系统提示: 104,857 (10.0%)
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 安全系数: 90.0%
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 有效预算: 660,603 tokens
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO -   - 预算利用率: 63.0%
2025-07-07 01:20:27,397 - resona.core.graph_builder - INFO - 图谱构建器初始化完成
2025-07-07 01:20:27,397 - resona.services.ai_service - INFO - 为模型 google/gemini-2.5-flash 启用 JSON 模式 (model_kwargs)
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO - AI 服务初始化完成:
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO -   - 模型: google/gemini-2.5-flash
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO -   - 描述: Google Gemini 2.5 Flash - 快速高效的对话模型
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO -   - JSON 模式: 启用
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO -   - 温度: 0.7
2025-07-07 01:20:28,716 - resona.services.ai_service - INFO -   - 最大输出: 8192
2025-07-07 01:20:29,534 - resona.core.semantic_analyzer - INFO - 语义解析器初始化完成
2025-07-07 01:20:29,534 - resona.core.user_profiler - INFO - 用户画像构建器初始化完成
2025-07-07 01:20:29,534 - graph_builder - INFO - 人格图谱构建器初始化完成
2025-07-07 01:20:29,534 - app - INFO - 图谱构建器初始化完成
2025-07-07 01:20:29,534 - url_parser - INFO - 开始解析Reddit链接: https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button
2025-07-07 01:20:29,534 - app - INFO - 链接解析成功，类型: comment
2025-07-07 01:20:29,534 - app - INFO - 开始数据抓取...
2025-07-07 01:20:29,534 - data_crawler - INFO - 开始从URL抓取数据: https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button
2025-07-07 01:20:29,534 - url_parser - INFO - 开始解析Reddit链接: https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button
2025-07-07 01:20:29,534 - data_crawler - INFO - 链接类型: comment
2025-07-07 01:20:29,534 - data_crawler - INFO - 从评论链接抓取数据: n1lgpq6
2025-07-07 01:20:31,374 - data_crawler - INFO - 评论作者: AtmosphereBrief6951
2025-07-07 01:20:31,374 - data_crawler - INFO - 开始抓取用户历史: AtmosphereBrief6951
2025-07-07 01:20:32,725 - data_crawler - INFO - 用户历史抓取完成: {'posts': 1, 'comments': 15, 'total_content': 16, 'original_posts': 1, 'original_comments': 16}
2025-07-07 01:20:32,725 - app - INFO - 数据抓取成功，用户: AtmosphereBrief6951
2025-07-07 01:20:32,725 - app - INFO - 开始语义分析...
2025-07-07 01:20:32,725 - semantic_analyzer - INFO - 开始分析用户内容: AtmosphereBrief6951
2025-07-07 01:20:32,725 - semantic_analyzer - INFO - 开始语义分析...
2025-07-07 01:20:32,725 - resona.core.semantic_analyzer - INFO - 开始解析用户输入：[评论 r/cursor] Awesome, glad it’s working for you t...
2025-07-07 01:20:32,725 - resona.core.semantic_analyzer - INFO - 尝试使用AI进行深度语义分析...
2025-07-07 01:20:32,725 - resona.core.semantic_analyzer - INFO - 使用 LangChain 语义分析链进行深度分析...
2025-07-07 01:20:50,041 - resona.core.semantic_analyzer - INFO - LangChain 分析成功，置信度: 0.950
2025-07-07 01:20:51,097 - resona.core.semantic_analyzer - INFO - AI分析成功，置信度: 0.950
2025-07-07 01:20:51,118 - resona.core.semantic_analyzer - INFO - 关键词英文化结果: ['Cursor AI', 'Copilot Insiders', 'GPT-4.1 Beast Mode', 'AI coding assistant comparison', 'structured instructions AI', 'developer workflow optimization', 'AI tool pricing', 'VS Code beta']    
2025-07-07 01:20:51,118 - resona.core.semantic_analyzer - INFO - 解析完成，提取到 8 个关键词，置信度: 0.950
2025-07-07 01:20:51,118 - resona.core.semantic_analyzer - INFO - 最终搜索关键词: ['cursor ai', 'copilot insiders', 'gpt-4.1 beast mode', 'ai coding assistant comparison', 'structured instructions ai', 'developer workflow optimization', 'ai tool pricing', 'vs code beta']      
2025-07-07 01:20:51,118 - semantic_analyzer - INFO - 开始深度用户特征分析...
2025-07-07 01:21:07,518 - openai._base_client - INFO - Retrying request to /chat/completions in 0.436443 seconds
2025-07-07 01:21:22,947 - openai._base_client - INFO - Retrying request to /chat/completions in 0.817761 seconds
2025-07-07 01:21:37,852 - openai._base_client - INFO - Retrying request to /chat/completions in 1.851120 seconds
2025-07-07 01:21:55,005 - resona.services.ai_service - ERROR - 获取完成时出错: Error code: 500 - {'error': {'message': 'inference exception'}}
2025-07-07 01:21:55,005 - resona.services.ai_service - WARNING - 使用部分JSON提取作为降级方案 - 响应长度: 14, think标签: False
2025-07-07 01:21:55,005 - semantic_analyzer - INFO - 开始内容模式分析...
2025-07-07 01:21:55,005 - semantic_analyzer - INFO - 用户内容分析完成: AtmosphereBrief6951
2025-07-07 01:21:55,005 - app - INFO - 语义分析成功，置信度: 0.950
2025-07-07 01:21:55,005 - app - INFO - 开始构建人格图谱...
2025-07-07 01:21:55,005 - graph_builder - INFO - 开始构建人格图谱: AtmosphereBrief6951
2025-07-07 01:21:55,017 - graph_builder - INFO - 构建基础用户图谱...
2025-07-07 01:21:55,019 - resona.core.graph_builder - INFO - 🚀 开始批量构建用户图谱，文本数量：16
2025-07-07 01:21:55,019 - resona.core.graph_builder - INFO - 🚀 开始批量分析 16 段内容
2025-07-07 01:21:55,019 - resona.core.graph_builder - INFO - 开始按Token预算打包 16 段内容
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO - 内容打包完成: 16 段 → 1 批次
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO - 预估总Token数: 1,496
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO -   批次 1: 16 段内容, ~1,496 tokens
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO -     预算: 892,641, 安全系数: 0.95, 节点上限: 10
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO - ⚡ 启动 1 个批量分析任务
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO - 📝 分析批次 1: 16 段内容
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO -   Token统计: 输入 1,496, 预算 892,641
2025-07-07 01:21:55,036 - resona.core.graph_builder - INFO -   自适应配置: 安全系数 0.95, 节点上限 10

[AI分析] 批次 1: 16 段内容

{
    "batches": [
        {
            "batch_index": 1,
            "nodes": [
                {
                    "id": "node_1_1",
                    "type": "experience",
                    "content": "经历烦人的警告",
                    "weight": 0.7,
                    "metadata": {
                        "confidence": 0.9,
                        "keywords": ["警告", "烦人"],
                        "temporal_evidence": "用户描述了过去经历的警告",
                        "time_period": "2025年07月",
                        "source_index": 1
                    }
                },
                2025-07-07 01:22:22,651 - resona.services.ai_service - ERROR - 流式返回出错: object of type 'NoneType' has no len()
2025-07-07 01:22:22,651 - resona.core.graph_builder - WARNING - AI服务调用失败: object of type 'NoneType' has no len()，批次 1 重试级别 0 
2025-07-07 01:22:22,651 - resona.core.graph_builder - WARNING - 🔄 批次 1 直接使用降级策略（重试机制已禁用）
2025-07-07 01:22:22,655 - resona.core.graph_builder - INFO - 使用降级策略逐个分析 16 段内容
2025-07-07 01:22:22,656 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：I also experienced this anno...
2025-07-07 01:22:22,659 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor发帖：I didn’t expect Copilot + VS...
2025-07-07 01:22:22,659 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Haha, fair enough but I prom...
2025-07-07 01:22:22,659 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：All good, mate I get it. The...
2025-07-07 01:22:22,662 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：That’s a fair point and I ag...
2025-07-07 01:22:22,663 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Nope, I was just using the s...
2025-07-07 01:22:22,663 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Sorry, I’m not able to post ...
2025-07-07 01:22:22,663 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Hey, I tried posting the pro...
2025-07-07 01:22:22,663 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：I’m on the Pro plan, which c...
2025-07-07 01:22:22,667 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Haha, fair point pricing and...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：I just add the instructions ...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：I just add the instructions ...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：I totally understand that an...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Hey, I get where you’re comi...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Haha, nice one. Anyway, if y...
2025-07-07 01:22:22,668 - resona.core.graph_builder - INFO - 开始分析文本内容：【2025年07月】在r/cursor评论：Awesome, glad it’s working f...
2025-07-07 01:22:23,732 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,732 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:23,780 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,780 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:23,825 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,825 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:23,853 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,853 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:23,897 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,898 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:23,944 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:23,944 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,001 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,002 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,040 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,040 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,073 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,073 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,106 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,106 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,163 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,163 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,206 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,206 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,239 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,239 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,275 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,275 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,306 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,306 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:24,337 - resona.core.graph_builder - INFO - 使用AI服务进行智能图谱分析
2025-07-07 01:22:24,337 - resona.core.graph_builder - INFO - 🧠 使用 LangChain 图谱分析链进行智能分析...
2025-07-07 01:22:32,947 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 3 个节点，2 条边
2025-07-07 01:22:32,980 - resona.core.graph_builder - INFO - 分析完成，提取到 3 个节点，2 条边
2025-07-07 01:22:36,323 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 5 个节点，5 条边
2025-07-07 01:22:36,323 - resona.core.graph_builder - INFO - 分析完成，提取到 5 个节点，5 条边
2025-07-07 01:22:38,787 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 4 个节点，4 条边
2025-07-07 01:22:38,787 - resona.core.graph_builder - INFO - 分析完成，提取到 4 个节点，4 条边
2025-07-07 01:22:38,787 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 4 个节点，4 条边
2025-07-07 01:22:38,787 - resona.core.graph_builder - INFO - 分析完成，提取到 4 个节点，4 条边
2025-07-07 01:22:41,110 - resona.core.graph_builder - ERROR - ❌ LangChain 图谱分析失败 (第 1 次): Invalid json output: {
  "nodes": [
    {
      "id": "node_1",
      "type": "experience",
      "content": "User experienced annoying warnings.",
      "weight": 0.8,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["annoying warnings"],
        "intensity": "medium"
      }
    を行い、その結果として「annoying」という感情が生じている。
    },
    {
      "id": "node_2",
      "type": "emotion",
      "content": "User feels annoyed.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["annoying"],
        "intensity": "high"
      }
    },
    {
      "id": "node_3",
      "type": "experience",
      "content": "User attempted to work after 3 days.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["tried to work"]
      }
    },
    {
      "id": "node_4",
      "type": "experience",
      "content": "User got rate limited with Sonnet 4.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "sonnet 4"]
      }
    },
    {
      "id": "node_5",
      "type": "experience",
      "content": "User switched to O3 model.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["switched to O3"]
      }
    },
    {
      "id": "node_6",
      "type": "experience",
      "content": "User got rate limited again with O3.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "O3"]
      }
    },
    {
      "id": "node_7",
      "type": "experience",
      "content": "User cannot use any models currently.",
      "weight": 1.0,
      "metadata": {
        "confidence": 1.0,
        "keywords": ["cannot use any models"],
        "intensity": "high"
      }
    },
    {
      "id": "node_8",
      "type": "topic",
      "content": "The problem of rate limiting.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["rate limited"]
      }
    }
  ],
  "edges": [
    {
      "source": "node_1",
      "target": "node_2",
      "relation": "triggers",
      "weight": 0.8,
      "evidence": "I also experienced this annoying warnings."
    },
    {
      "source": "node_3",
      "target": "node_4",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "After 3 days i tried to work and after 5 - 6 prompts i got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_5",
      "relation": "causes",
      "weight": 0.85,
      "evidence": "then I switched to O3"
    },
    {
      "source": "node_5",
      "target": "node_6",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "after 2-3 prompts again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_7",
      "relation": "causes",
      "weight": 1.0,
      "evidence": "and now i can not use any models."
    },
    {
      "source": "node_8",
      "target": "node_7",
      "relation": "influences",
      "weight": 0.9,
      "evidence": "The recurring 'rate limited' issue leads to the inability to use any models."
    }
  ]
}
2025-07-07 01:22:42,121 - resona.core.graph_builder - INFO - 🔄 第 2 次尝试 LangChain 图谱分析...
2025-07-07 01:22:42,803 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 6 个节点，5 条边
2025-07-07 01:22:42,804 - resona.core.graph_builder - INFO - 分析完成，提取到 6 个节点，5 条边
2025-07-07 01:22:44,004 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 5 个节点，4 条边
2025-07-07 01:22:44,004 - resona.core.graph_builder - INFO - 分析完成，提取到 5 个节点，4 条边
2025-07-07 01:22:44,033 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 8 个节点，7 条边
2025-07-07 01:22:44,033 - resona.core.graph_builder - INFO - 分析完成，提取到 8 个节点，7 条边
2025-07-07 01:22:44,063 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 5 个节点，5 条边
2025-07-07 01:22:44,063 - resona.core.graph_builder - INFO - 分析完成，提取到 5 个节点，5 条边
2025-07-07 01:22:44,141 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 5 个节点，5 条边
2025-07-07 01:22:44,141 - resona.core.graph_builder - INFO - 分析完成，提取到 5 个节点，5 条边
2025-07-07 01:22:45,028 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 7 个节点，6 条边
2025-07-07 01:22:45,028 - resona.core.graph_builder - INFO - 分析完成，提取到 7 个节点，6 条边
2025-07-07 01:22:45,238 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 3 个节点，2 条边
2025-07-07 01:22:45,238 - resona.core.graph_builder - INFO - 分析完成，提取到 3 个节点，2 条边
2025-07-07 01:22:45,852 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 8 个节点，6 条边
2025-07-07 01:22:45,864 - resona.core.graph_builder - INFO - 分析完成，提取到 8 个节点，6 条边
2025-07-07 01:22:47,407 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 10 个节点，8 条边
2025-07-07 01:22:47,407 - resona.core.graph_builder - INFO - 分析完成，提取到 10 个节点，8 条边
2025-07-07 01:22:51,688 - resona.core.graph_builder - INFO - ✅ LangChain 图谱分析成功，提取到 10 个节点，11 条边
2025-07-07 01:22:51,688 - resona.core.graph_builder - INFO - 分析完成，提取到 10 个节点，11 条边
2025-07-07 01:22:54,163 - resona.core.graph_builder - WARNING - ⏰ LangChain 分析超时 (第 1 次)
2025-07-07 01:22:56,170 - resona.core.graph_builder - INFO - 🔄 第 2 次尝试 LangChain 图谱分析...
2025-07-07 01:23:01,123 - resona.core.graph_builder - ERROR - ❌ LangChain 图谱分析失败 (第 2 次): Invalid json output: {
  "nodes": [
    {
      "id": "node_1",
      "type": "experience",
      "content": "User experienced annoying warnings.",
      "weight": 0.8,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["annoying warnings"],
        "intensity": "medium"
      }
    を行い、その結果として「annoying」という感情が生じている。
    },
    {
      "id": "node_2",
      "type": "emotion",
      "content": "User feels annoyed.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["annoying"],
        "intensity": "high"
      }
    },
    {
      "id": "node_3",
      "type": "experience",
      "content": "User attempted to work after 3 days.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["tried to work"]
      }
    },
    {
      "id": "node_4",
      "type": "experience",
      "content": "User got rate limited with Sonnet 4.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "sonnet 4"]
      }
    },
    {
      "id": "node_5",
      "type": "experience",
      "content": "User switched to O3 model.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["switched to O3"]
      }
    },
    {
      "id": "node_6",
      "type": "experience",
      "content": "User got rate limited again with O3.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "O3"]
      }
    },
    {
      "id": "node_7",
      "type": "experience",
      "content": "User cannot use any models currently.",
      "weight": 1.0,
      "metadata": {
        "confidence": 1.0,
        "keywords": ["cannot use any models"],
        "intensity": "high"
      }
    },
    {
      "id": "node_8",
      "type": "topic",
      "content": "The problem of rate limiting.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["rate limited"]
      }
    }
  ],
  "edges": [
    {
      "source": "node_1",
      "target": "node_2",
      "relation": "triggers",
      "weight": 0.8,
      "evidence": "I also experienced this annoying warnings."
    },
    {
      "source": "node_3",
      "target": "node_4",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "After 3 days i tried to work and after 5 - 6 prompts i got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_5",
      "relation": "causes",
      "weight": 0.85,
      "evidence": "then I switched to O3"
    },
    {
      "source": "node_5",
      "target": "node_6",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "after 2-3 prompts again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_7",
      "relation": "causes",
      "weight": 1.0,
      "evidence": "and now i can not use any models."
    },
    {
      "source": "node_8",
      "target": "node_7",
      "relation": "influences",
      "weight": 0.9,
      "evidence": "The recurring 'rate limited' issue leads to the inability to use any models."
    }
  ]
}
2025-07-07 01:23:02,133 - resona.core.graph_builder - INFO - 🔄 第 3 次尝试 LangChain 图谱分析...
2025-07-07 01:23:19,620 - resona.core.graph_builder - ERROR - ❌ LangChain 图谱分析失败 (第 3 次): Invalid json output: {
  "nodes": [
    {
      "id": "node_1",
      "type": "experience",
      "content": "User experienced annoying warnings.",
      "weight": 0.8,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["annoying warnings"],
        "intensity": "medium"
      }
    を行い、その結果として「annoying」という感情が生じている。
    },
    {
      "id": "node_2",
      "type": "emotion",
      "content": "User feels annoyed.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["annoying"],
        "intensity": "high"
      }
    },
    {
      "id": "node_3",
      "type": "experience",
      "content": "User attempted to work after 3 days.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["tried to work"]
      }
    },
    {
      "id": "node_4",
      "type": "experience",
      "content": "User got rate limited with Sonnet 4.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "sonnet 4"]
      }
    },
    {
      "id": "node_5",
      "type": "experience",
      "content": "User switched to O3 model.",
      "weight": 0.7,
      "metadata": {
        "confidence": 0.8,
        "keywords": ["switched to O3"]
      }
    },
    {
      "id": "node_6",
      "type": "experience",
      "content": "User got rate limited again with O3.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.95,
        "keywords": ["rate limited", "O3"]
      }
    },
    {
      "id": "node_7",
      "type": "experience",
      "content": "User cannot use any models currently.",
      "weight": 1.0,
      "metadata": {
        "confidence": 1.0,
        "keywords": ["cannot use any models"],
        "intensity": "high"
      }
    },
    {
      "id": "node_8",
      "type": "topic",
      "content": "The problem of rate limiting.",
      "weight": 0.9,
      "metadata": {
        "confidence": 0.9,
        "keywords": ["rate limited"]
      }
    }
  ],
  "edges": [
    {
      "source": "node_1",
      "target": "node_2",
      "relation": "triggers",
      "weight": 0.8,
      "evidence": "I also experienced this annoying warnings."
    },
    {
      "source": "node_3",
      "target": "node_4",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "After 3 days i tried to work and after 5 - 6 prompts i got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "got rate limited with sonnet 4 thinking"
    },
    {
      "source": "node_4",
      "target": "node_5",
      "relation": "causes",
      "weight": 0.85,
      "evidence": "then I switched to O3"
    },
    {
      "source": "node_5",
      "target": "node_6",
      "relation": "triggers",
      "weight": 0.9,
      "evidence": "after 2-3 prompts again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_8",
      "relation": "supports",
      "weight": 0.8,
      "evidence": "again got the rate limited"
    },
    {
      "source": "node_6",
      "target": "node_7",
      "relation": "causes",
      "weight": 1.0,
      "evidence": "and now i can not use any models."
    },
    {
      "source": "node_8",
      "target": "node_7",
      "relation": "influences",
      "weight": 0.9,
      "evidence": "The recurring 'rate limited' issue leads to the inability to use any models."
    }
  ]
}
2025-07-07 01:23:19,620 - resona.core.graph_builder - WARNING - 🔄 达到最大重试次数，使用降级策略
2025-07-07 01:23:19,620 - resona.core.graph_builder - INFO - 🔄 使用增强降级图谱构建策略
2025-07-07 01:23:19,620 - resona.core.graph_builder - WARNING - 关键词匹配未识别到任何节点，创建基本通用节点
2025-07-07 01:23:19,620 - resona.core.graph_builder - INFO - ✅ 增强降级分析完成: 2 个节点，1 条边
2025-07-07 01:23:19,620 - resona.core.graph_builder - INFO - 分析完成，提取到 2 个节点，1 条边
2025-07-07 01:23:26,199 - resona.core.graph_builder - WARNING - ⏰ LangChain 分析超时 (第 2 次)
2025-07-07 01:23:28,215 - resona.core.graph_builder - INFO - 🔄 第 3 次尝试 LangChain 图谱分析...
2025-07-07 01:23:58,228 - resona.core.graph_builder - WARNING - ⏰ LangChain 分析超时 (第 3 次)
2025-07-07 01:23:58,228 - resona.core.graph_builder - ERROR - ❌ LangChain 图谱分析失败 (第 3 次): LangChain分析超时，已达最大重试次数    
2025-07-07 01:23:58,228 - resona.core.graph_builder - WARNING - 🔄 达到最大重试次数，使用降级策略
2025-07-07 01:23:58,228 - resona.core.graph_builder - INFO - 🔄 使用增强降级图谱构建策略
2025-07-07 01:23:58,233 - resona.core.graph_builder - WARNING - 关键词匹配未识别到任何节点，创建基本通用节点
2025-07-07 01:23:58,233 - resona.core.graph_builder - INFO - ✅ 增强降级分析完成: 2 个节点，1 条边
2025-07-07 01:23:58,233 - resona.core.graph_builder - INFO - 分析完成，提取到 2 个节点，1 条边
2025-07-07 01:23:58,233 - resona.core.graph_builder - INFO - 开始合并 16 个图谱元素集合
2025-07-07 01:23:58,240 - resona.core.graph_builder - INFO - 合并完成：87 个节点，76 条边
2025-07-07 01:23:58,241 - resona.core.graph_builder - INFO - 开始合并 1 个图谱元素集合
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - 合并完成：87 个节点，76 条边
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - ✅ 批量分析完成: 1/1 批次成功
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - 📊 批量分析完成，耗时: 123.22秒
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - ✅ 用户图谱批量构建完成：52 个节点，76 条边，总耗时: 123.22秒
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - ✅ 批量分析完成: 1/1 批次成功
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - 📊 批量分析完成，耗时: 123.22秒
2025-07-07 01:23:58,243 - resona.core.graph_builder - INFO - ✅ 用户图谱批量构建完成：52 个节点，76 条边，总耗时: 123.22秒
2025-07-07 01:23:58,243 - graph_builder - INFO - 增强图谱结构...
2025-07-07 01:23:58,251 - graph_builder - INFO - 节点连接增强完成，边数：76
2025-07-07 01:23:58,251 - graph_builder - INFO - 图谱增强完成，节点数：60
2025-07-07 01:23:58,251 - graph_builder - INFO - 构建完整用户画像...
2025-07-07 01:23:58,251 - graph_builder - INFO - 生成图谱解释...
2025-07-07 01:24:16,372 - graph_builder - INFO - 人格图谱构建完成: AtmosphereBrief6951
2025-07-07 01:24:16,372 - app - INFO - 图谱构建成功，节点数: 60
2025-07-07 01:24:16,482 - app - INFO - 已持久化分析结果：AtmosphereBrief6951
2025-07-07 01:24:16,484 - app - INFO - 分析完成，用户: AtmosphereBrief6951
2025-07-07 01:24:16,533 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 01:24:16] "POST /api/analyze HTTP/1.1" 200 -
2025-07-07 01:24:39,415 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 01:24:39] "GET / HTTP/1.1" 200 -
2025-07-07 01:24:39,470 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 01:24:39] "GET /static/css/style.css HTTP/1.1" 304 -
2025-07-07 01:24:39,477 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 01:24:39] "GET /static/js/app.js HTTP/1.1" 304 -
2025-07-07 01:24:39,815 - werkzeug - INFO - 127.0.0.1 - - [07/Jul/2025 01:24:39] "GET /api/last_result HTTP/1.1" 200 -

